FROM public.ecr.aws/amazonlinux/amazonlinux:latest

ENV PYTHONUNBUFFERED 1

# Install system dependencies
RUN yum update -y && \
    yum install -y --allowerasing \
    git \
    gcc \
    make \
    openssl-devel \
    bzip2-devel \
    libffi-devel \
    zlib-devel \
    readline-devel \
    sqlite-devel \
    wget \
    tar \
    gzip \
    which \
    postgresql-devel \
    libmemcached-devel && \
    yum clean all

# Install Node.js and npm
RUN curl -fsSL https://rpm.nodesource.com/setup_lts.x | bash - && \
    yum install -y nodejs

# Install pyenv
RUN curl https://pyenv.run | bash

# Add pyenv to PATH
ENV PATH="/root/.pyenv/bin:$PATH"
ENV PYENV_ROOT="/root/.pyenv"

# Configure shell to load pyenv
RUN echo 'export PATH="/root/.pyenv/bin:$PATH"' >> ~/.bashrc && \
    echo 'eval "$(pyenv init --path)"' >> ~/.bashrc && \
    echo 'eval "$(pyenv init -)"' >> ~/.bashrc

RUN mkdir /code
WORKDIR /code

RUN mkdir /root/.jupyter/

# Install Python 3.12 and set as global (compatible with Django 4.2)
RUN /root/.pyenv/bin/pyenv install 3.12.0 && \
    /root/.pyenv/bin/pyenv global 3.12.0

# Upgrade pip first to handle newer pyproject.toml formats
RUN /root/.pyenv/versions/3.12.0/bin/pip install --upgrade pip

# Install Poetry only - let Poetry manage all Python packages
RUN /root/.pyenv/versions/3.12.0/bin/pip install poetry

# Install less globally and build tools for Vue 3
RUN npm install -g less

# Install dockerize for waiting on dependencies
RUN wget https://github.com/jwilder/dockerize/releases/download/v0.9.3/dockerize-linux-arm64-v0.9.3.tar.gz \
    && tar -C /usr/local/bin -xzvf dockerize-linux-arm64-v0.9.3.tar.gz \
    && rm dockerize-linux-arm64-v0.9.3.tar.gz

# Add main poetry file (consolidated configuration)
ADD pyproject.toml /code/pyproject.toml

# Configure poetry with authentication for private repository
RUN /root/.pyenv/versions/3.12.0/bin/poetry config repositories.ws https://pypi.wheel-size.com/
RUN /root/.pyenv/versions/3.12.0/bin/poetry config http-basic.ws repo-user Ojc3ZSPwIBEEisX

# Set environment variables to prefer binary wheels and set pg_config path
ENV PIP_PREFER_BINARY=1
ENV PATH="/usr/pgsql-*/bin:/usr/bin:$PATH"

# Use Poetry to install all dependencies (including WS packages from private PyPI)
RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false
RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false
# Install dependencies from Docker-specific pyproject.toml
RUN /root/.pyenv/versions/3.12.0/bin/poetry install --no-root
# Verify that django-turnstile is installed correctly
RUN /root/.pyenv/versions/3.12.0/bin/python -c "import turnstile; print('Turnstile module imported successfully')"

# All dependencies are now managed by Poetry from pyproject.toml
# No manual pip installs needed - Poetry handles everything including:
# - jupyter (for development)
# - django-recaptcha, django-admin-interface, django-registration-redux
# - All WS packages from private PyPI

# WS packages are now installed via Poetry from private PyPI repository

# Copy source code for Vue 3 build
COPY src/apps/widgets/finder_v2/app/package.json /code/src/apps/widgets/finder_v2/app/package.json

# Install Vue 3 dependencies
WORKDIR /code/src/apps/widgets/finder_v2/app
RUN npm ci --only=production

# Copy Vue 3 source files
COPY src/apps/widgets/finder_v2/app/ /code/src/apps/widgets/finder_v2/app/

# Build Vue 3 app for production
RUN npm run build

# Return to main working directory
WORKDIR /code
