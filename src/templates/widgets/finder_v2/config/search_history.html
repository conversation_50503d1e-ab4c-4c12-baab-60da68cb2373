{% load i18n %}
{% load form_ext %}

<!-- Search History Configuration Section -->
<div class="config-section">
  <h3 class="config-section-title">
    <i class="fa fa-history"></i>
    {% trans 'Search History Settings' %}
  </h3>
  
  <div class="config-section-content">
    <div class="row">
      <div class="col-md-12">
        <p class="config-section-description">
          {% trans 'Configure how search history behaves for users of this widget. Search history allows users to save and quickly re-execute their recent vehicle searches.' %}
        </p>
      </div>
    </div>

    <div class="row">
      <!-- Enable Search History -->
      <div class="col-md-6">
        <div class="form-group">
          <label class="form-label">
            {{ form.search_history.enabled.label }}
            {% if form.search_history.enabled.help_text %}
              <i class="fa fa-question-circle help-tooltip" 
                 title="{{ form.search_history.enabled.help_text }}"></i>
            {% endif %}
          </label>
          <div class="form-control-wrapper">
            {{ form.search_history.enabled }}
            {% if form.search_history.enabled.errors %}
              <div class="form-error">
                {{ form.search_history.enabled.errors.0 }}
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Show Timestamps -->
      <div class="col-md-6">
        <div class="form-group">
          <label class="form-label">
            {{ form.search_history.show_timestamps.label }}
            {% if form.search_history.show_timestamps.help_text %}
              <i class="fa fa-question-circle help-tooltip" 
                 title="{{ form.search_history.show_timestamps.help_text }}"></i>
            {% endif %}
          </label>
          <div class="form-control-wrapper">
            {{ form.search_history.show_timestamps }}
            {% if form.search_history.show_timestamps.errors %}
              <div class="form-error">
                {{ form.search_history.show_timestamps.errors.0 }}
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- Maximum Stored Searches -->
      <div class="col-md-4">
        <div class="form-group">
          <label class="form-label">
            {{ form.search_history.max_items.label }}
            {% if form.search_history.max_items.help_text %}
              <i class="fa fa-question-circle help-tooltip" 
                 title="{{ form.search_history.max_items.help_text }}"></i>
            {% endif %}
          </label>
          <div class="form-control-wrapper">
            {{ form.search_history.max_items }}
            {% if form.search_history.max_items.errors %}
              <div class="form-error">
                {{ form.search_history.max_items.errors.0 }}
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Default Display Items -->
      <div class="col-md-4">
        <div class="form-group">
          <label class="form-label">
            {{ form.search_history.display_items.label }}
            {% if form.search_history.display_items.help_text %}
              <i class="fa fa-question-circle help-tooltip" 
                 title="{{ form.search_history.display_items.help_text }}"></i>
            {% endif %}
          </label>
          <div class="form-control-wrapper">
            {{ form.search_history.display_items }}
            {% if form.search_history.display_items.errors %}
              <div class="form-error">
                {{ form.search_history.display_items.errors.0 }}
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Auto-expand Panel -->
      <div class="col-md-4">
        <div class="form-group">
          <label class="form-label">
            {{ form.search_history.auto_expand.label }}
            {% if form.search_history.auto_expand.help_text %}
              <i class="fa fa-question-circle help-tooltip" 
                 title="{{ form.search_history.auto_expand.help_text }}"></i>
            {% endif %}
          </label>
          <div class="form-control-wrapper">
            {{ form.search_history.auto_expand }}
            {% if form.search_history.auto_expand.errors %}
              <div class="form-error">
                {{ form.search_history.auto_expand.errors.0 }}
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Search History Preview -->
    <div class="row mt-4">
      <div class="col-md-12">
        <div class="search-history-preview">
          <h4 class="preview-title">
            <i class="fa fa-eye"></i>
            {% trans 'Search History Preview' %}
          </h4>
          <div class="preview-content">
            <div class="mock-search-history">
              <div class="search-history-header">
                <span class="panel-title">{% trans 'Recent Searches' %}</span>
                <span class="expand-icon">▼</span>
              </div>
              <div class="search-history-list">
                <div class="search-item">
                  <div class="search-content">
                    <div class="search-description">2021 BMW X5 xDrive40i</div>
                    <div class="search-time">2 hours ago</div>
                  </div>
                  <button class="remove-button">✕</button>
                </div>
                <div class="search-item">
                  <div class="search-content">
                    <div class="search-description">2020 Toyota Camry LE</div>
                    <div class="search-time">1 day ago</div>
                  </div>
                  <button class="remove-button">✕</button>
                </div>
                <div class="search-item">
                  <div class="search-content">
                    <div class="search-description">2019 Honda Civic LX</div>
                    <div class="search-time">3 days ago</div>
                  </div>
                  <button class="remove-button">✕</button>
                </div>
              </div>
              <div class="show-more-container">
                <button class="show-more-button">Show More (2 more)</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Usage Instructions -->
    <div class="row mt-4">
      <div class="col-md-12">
        <div class="config-info-box">
          <h4 class="info-title">
            <i class="fa fa-info-circle"></i>
            {% trans 'How Search History Works' %}
          </h4>
          <ul class="info-list">
            <li>{% trans 'Search history is stored locally in the user\'s browser using localStorage' %}</li>
            <li>{% trans 'Users can click on any previous search to instantly re-execute it' %}</li>
            <li>{% trans 'Search data is automatically cleaned and deduplicated' %}</li>
            <li>{% trans 'No personal data is sent to servers - everything stays in the user\'s browser' %}</li>
            <li>{% trans 'History gracefully degrades if localStorage is not available' %}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.config-section {
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #fff;
}

.config-section-title {
  margin: 0;
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 8px 8px 0 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
}

.config-section-title i {
  margin-right: 0.5rem;
  color: #4299e1;
}

.config-section-content {
  padding: 1.5rem;
}

.config-section-description {
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.form-label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  display: block;
}

.help-tooltip {
  color: #6b7280;
  cursor: help;
  margin-left: 0.25rem;
}

.form-control-wrapper {
  position: relative;
}

.form-error {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.search-history-preview {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: #f8fafc;
  padding: 1rem;
}

.preview-title {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
}

.preview-title i {
  margin-right: 0.5rem;
  color: #6366f1;
}

.mock-search-history {
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
  max-width: 400px;
}

.search-history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.expand-icon {
  font-size: 0.75rem;
  color: #6b7280;
}

.search-history-list {
  padding: 0.5rem;
}

.search-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.15s ease;
}

.search-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.search-item:last-child {
  margin-bottom: 0;
}

.search-content {
  flex: 1;
}

.search-description {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.search-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.remove-button {
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #6b7280;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
}

.remove-button:hover {
  background: #fee2e2;
  color: #dc2626;
}

.show-more-container {
  display: flex;
  justify-content: center;
  padding: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

.show-more-button {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: #fff;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.15s ease;
}

.show-more-button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.config-info-box {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 1rem;
}

.info-title {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 500;
  color: #0369a1;
}

.info-title i {
  margin-right: 0.5rem;
}

.info-list {
  margin: 0;
  padding-left: 1.25rem;
  color: #075985;
  line-height: 1.6;
}

.info-list li {
  margin-bottom: 0.25rem;
}

.info-list li:last-child {
  margin-bottom: 0;
}
</style>