{% extends 'portal/base.html' %}

{% load static %}
{% load i18n %}
{% load flatpages %}
{% load jsonify %}
{% load inline_static %}
{% load dict_utils %}
{% load form_ext %}

{% block title %}
  {% if request.resolver_match.url_name == 'configure-demo' %}
    {% trans 'Demo Widget Configuration' %} - Finder V2
  {% else %}
    {% trans 'Widget Configuration' %} - Finder V2
  {% endif %}
{% endblock %}

{% block extra_css %}
  <link rel="stylesheet" href="{% static 'widget/css/widget-cc.css' %}"/>
  <link rel="stylesheet" href="{% static 'portal/css/hljs/default.min.css' %}">
  <link rel="stylesheet" href="{% static 'portal/css/hljs/hybrid.css' %}">
  {{ form.media }}
  <style>
    .tag-cloud {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 10px;
    }
    
    .tag-cloud span {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 6px 12px;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .tag-cloud span:hover {
      background: #e9ecef;
      border-color: #adb5bd; 
    }
    
    .tag-cloud span.active {
      background: #f8f9fa;
      border-color: var(--color-ws-primary-900);
      color: var(--color-ws-primary-700);
    }

    .regions-filter     .tag-cloud span {
      opacity: 0.6;
      margin: 2px;
    }
    
    .regions-filter .tag-cloud span.active {
      opacity: 1;
      font-weight: bold;
      background-color: #dedede;
    }

    .tag-cloud span a {
      text-decoration: none;
      color: inherit;
    }
    
    .loading-state {
      padding: 20px;
      text-align: center;
      color: #6c757d;
      font-style: italic;
    }
    
    .error-state {
      padding: 20px;
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 4px;
      color: #721c24;
    }
  </style>
{% endblock extra_css %}

{% block extra_js %}
  <script src="{% static 'portal/js/highlight.min.js' %}"></script>
  <script>hljs.initHighlightingOnLoad();</script>
{% endblock extra_js %}

{% block content %}
  <div class="mb40">

<!-- 
    <div class="overflow-hidden rounded-lg bg-white shadow-sm mb-5">
      <h2 class="sr-only" id="profile-overview-title">
        {% if request.resolver_match.url_name == 'configure-demo' %}
        {% trans 'Demo Widget Configuration' %} - Finder V2
      {% else %}
        {% trans 'Widget Configuration' %} - Finder V2
      {% endif %}
      </h2>
      <div class="bg-white p-6">
        <div class="sm:flex sm:items-center sm:justify-between">
          <div class="sm:flex sm:space-x-5">
            <div class="shrink-0">
              <img class="mx-auto size-20 rounded-full" src="https://images.unsplash.com/photo-1550525811-e5869dd03032?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
            </div>
            <div class="mt-4 text-center sm:mt-0 sm:pt-1 sm:text-left">
              <p class="text-sm font-medium text-gray-600">
                {% if request.resolver_match.url_name == 'configure-demo' %}
                {% blocktrans %}
                Demo Mode 
                {% endblocktrans %}
              {% else %}
                {% blocktrans %}
                Live Configuration
                {% endblocktrans %}
              {% endif %}
              </p>
              <p class="text-xl font-bold text-gray-900 sm:text-2xl">
                {% if request.resolver_match.url_name == 'configure-demo' %}
                {% blocktrans %}
                Advanced Vehicle Search Builder
                {% endblocktrans %}
              {% else %}
                {% blocktrans %}
                Advanced Vehicle Search Builder
                {% endblocktrans %}
              {% endif %}
              
              </p>
              <p class="text-sm font-medium text-gray-600">

              {% if request.resolver_match.url_name == 'configure-demo' %}
                {% blocktrans %}
                Tailor your vehicle search widget. You can filter by brand and region, select a search flow, and customize the appearance to match your site.
                {% endblocktrans %}
              {% else %}
                {% blocktrans %}
                Tailor your vehicle search widget. You can filter by brand and region, select a search flow, and customize the appearance to match your site.
                {% endblocktrans %}
              {% endif %}
              </p>
            </div>
          </div>
          <div class="mt-5 flex justify-center sm:mt-0">
            <a href="#" class="flex items-center justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50">View profile</a>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-1 divide-y divide-gray-200 border-t border-gray-200 bg-gray-50 sm:grid-cols-3 sm:divide-x sm:divide-y-0">
        <div class="px-6 py-5 text-center text-sm font-medium">
          <span class="text-gray-900">12</span>
          <span class="text-gray-600">Vacation days left</span>
        </div>
        <div class="px-6 py-5 text-center text-sm font-medium">
          <span class="text-gray-900">4</span>
          <span class="text-gray-600">Sick days left</span>
        </div>
        <div class="px-6 py-5 text-center text-sm font-medium">
          <span class="text-gray-900">2</span>
          <span class="text-gray-600">Personal days left</span>
        </div>
      </div>
    </div> -->
    




    <!-- Success/Error Messages -->
    {% if messages %}
      {% for message in messages %}
        {% if message.tags == 'success' %}
          <div class="rounded-md bg-green-50 p-4 mb-6 border border-green-200">
            <div class="flex">
              <div class="shrink-0">
                <svg class="size-5 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">{% trans 'Success' %}</h3>
                <div class="mt-2 text-sm text-green-700">
                  <p>{{ message }}</p>
                </div>
              </div>
            </div>
          </div>
        {% elif message.tags == 'error' %}
          <div class="rounded-md bg-red-50 p-4 mb-6 border border-red-200">
            <div class="flex">
              <div class="shrink-0">
                <svg class="size-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM8.28 7.22a.75.75 0 0 0-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 1 0 1.06 1.06L10 11.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L11.06 10l1.72-1.72a.75.75 0 0 0-1.06-1.06L10 8.94 8.28 7.22Z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">{% trans 'Error' %}</h3>
                <div class="mt-2 text-sm text-red-700">
                  <p>{{ message }}</p>
                </div>
              </div>
            </div>
          </div>
        {% elif message.tags == 'warning' %}
          <div class="rounded-md bg-yellow-50 p-4 mb-6 border border-yellow-200">
            <div class="flex">
              <div class="shrink-0">
                <svg class="size-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                  <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495ZM10 5a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 10 5Zm0 9a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">{% trans 'Warning' %}</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>{{ message }}</p>
                </div>
              </div>
            </div>
          </div>
        {% else %}
          <div class="rounded-md bg-blue-50 p-4 mb-6 border border-blue-200">
            <div class="flex">
              <div class="shrink-0">
                <svg class="size-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-7-4a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM9 9a.75.75 0 0 0 0 1.5h.253a.25.25 0 0 1 .244.304l-.459 2.066A1.75 1.75 0 0 0 10.747 15H11a.75.75 0 0 0 0-1.5h-.253a.25.25 0 0 1-.244-.304l.459-2.066A1.75 1.75 0 0 0 9.253 9H9Z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">{% trans 'Info' %}</h3>
                <div class="mt-2 text-sm text-blue-700">
                  <p>{{ message }}</p>
                </div>
              </div>
            </div>
          </div>
        {% endif %}
      {% endfor %}
    {% endif %}

    <form class="config-form" action="{% if request.resolver_match.url_name == 'configure-demo' %}{% url 'widget:configure-demo' object.slug %}{% else %}{% url 'widget:configure' object.slug %}{% endif %}" method="post">
      {% csrf_token %}

      {% if form.is_bound and not form.is_valid %}
        <div class="alert alert-danger" role="alert">{% trans 'Please, correct errors below' %}
          {% if form.errors or form.forms %}
            <ul class="mt-2 text-sm list-disc list-inside text-red-600 space-y-1">
              {# Top-level errors (non-field) #}
              {% for field, errors in form.errors.items %}
                {% for error in errors %}
                  <li>{{ error }}</li>
                {% endfor %}
              {% endfor %}

              {# Dive into MultiModelForm sub-forms to surface their errors #}
              {% for s_name, s_form in form.forms.items %}
                {% for field, errors in s_form.errors.items %}
                  {% for error in errors %}
                    <li><strong>{{ field|capfirst }}</strong>: {{ error }}</li>
                  {% endfor %}
                {% endfor %}
              {% endfor %}
            </ul>
          {% endif %}
        </div>
      {% endif %}

      {% include 'widgets/common/config/config.html' %}
      {% include 'widgets/finder_v2/demo/content.html' %}
      {% include 'widgets/finder_v2/interface.html' %}
      {% include 'widgets/finder_v2/config/search_history.html' %}
      {% include 'widgets/finder_v2/config/theme.html' %}

      <!-- Add permissions section for authenticated config endpoint -->
      {% if request.resolver_match.url_name == 'configure' or not form.config.is_demo %}
        {% include 'widgets/finder_v2/config/permissions.html' %}
      {% endif %}

      <div class="row mt-40">
        <div class="form-group col-md-12 submit-row">
          <div class="submit-item">
            <input type="submit" 
                   value="{% if request.resolver_match.url_name == 'configure-demo' %}{% trans 'Update Demo Configuration' %}{% else %}{% trans 'Update Configuration' %}{% endif %}"
                   class="btn btn-lg btn-primary"/>
          </div>
        </div>
      </div>
    </form>

    <div class="iframe-preview">
      <h3 class="">
        <span>{% trans 'Preview' %}</span>
        {% if not object.is_default %}
          <span class="pull-right">
            <a href="{% url 'widget:iframe' object.slug %}" target="_blank"
               class="fa fa-eye"></a>
          </span>
        {% endif %}
      </h3>
      <iframe frameborder="0"
        src="{% url 'widget:iframe' object.slug %}?config"
        data-width="{{ form.interface.width.value|default:'900' }}"
        width="{{ form.interface.width.value|default:'900' }}"
        autoresize="1"
        scrolling="no">
      </iframe>
      <script src="{% static 'finder_v2/js/libs/iframeResizer.min.js' %}"></script>
      <script>
        // Initialize iframeResizer for the preview iframe once the script is loaded
        document.addEventListener('DOMContentLoaded', function () {
          var previewFrame = document.querySelector('.iframe-preview iframe');
          if (previewFrame && typeof iFrameResize === 'function') {
            iFrameResize({
              checkOrigin: false,
              heightCalculationMethod: 'taggedElement',
              scrolling: false
            }, previewFrame);
          }
        });
      </script>
    </div>
  </div>

  <!-- Plain JavaScript functionality -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Finder V2 Config - Plain JS initialization');

      // Initialize tag choice functionality
      initTagChoiceControls();

      // Initialize tab functionality
      initTabControls();

      // Initialize iframe preview updates
      initPreviewUpdates();

      // Finder-v2: remove legacy height field from UI
      const heightGroup = document.querySelector('input[name="interface-height"]')?.closest('.form-group');
      if (heightGroup) {
        heightGroup.remove();
      }

      // Add timeout to show error if Vue app doesn't load
      setTimeout(function() {
        // ... existing code ...
      }, 5000);
    });

    function initTagChoiceControls() {
      console.log('Initializing tag choice controls');

      // Handle all tag cloud controls
      document.querySelectorAll('.tag-cloud').forEach(function(cloud) {
        // Accept both text and hidden inputs so configuration page works for all fields
        let hiddenInput = cloud.parentElement.querySelector('input[type="text"][name="content-' + cloud.dataset.field + '"]');
        if (!hiddenInput) {
          hiddenInput = cloud.parentElement.querySelector('input[type="hidden"][name="content-' + cloud.dataset.field + '"]');
        }
        if (!hiddenInput) return;

        const tags = {};

        // Parse existing values
        try {
          const existingValue = hiddenInput.value;
          console.log('🎯 JS DEBUG: Field:', hiddenInput.name, 'Raw value:', JSON.stringify(existingValue), 'Type:', typeof existingValue);
          
          if (existingValue) {
            existingValue.split(',').forEach(function(tag) {
              if (tag.trim()) {
                tags[tag.trim()] = true;
                console.log('🎯 JS DEBUG: Marking tag as active:', tag.trim());
              }
            });
          }
          
          console.log('🎯 JS DEBUG: Final tags object:', tags);
        } catch (e) {
          console.log('Could not parse existing tags:', e);
        }

        // Handle tag clicks
        cloud.querySelectorAll('span[data-slug]').forEach(function(span) {
          const slug = span.dataset.slug;

          // Set initial state
          if (tags[slug]) {
            span.classList.add('active');
          }

          span.addEventListener('click', function(e) {
            e.preventDefault();

            // Toggle tag
            if (tags[slug]) {
              delete tags[slug];
              span.classList.remove('active');
            } else {
              tags[slug] = true;
              span.classList.add('active');
            }

            // Update hidden input (works with both hidden and text inputs)
            const tagArray = Object.keys(tags);
            hiddenInput.value = tagArray.join(',');

            console.log('Updated tags for', hiddenInput.name, ':', tagArray);
          });
        });
      });
    }

    function initTabControls() {
      console.log('Initializing tab controls');

      // Handle filter type radio buttons
      document.querySelectorAll('input[name="content-by"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
          // Hide all tab panes
          document.querySelectorAll('.tab-pane').forEach(function(pane) {
            pane.classList.remove('active');
          });

          // Show selected tab
          if (this.value) {
            const targetTab = document.getElementById('tab-' + this.value);
            if (targetTab) {
              targetTab.classList.add('active');
            }
          }

          // Update button states
          document.querySelectorAll('.btn-group label').forEach(function(label) {
            label.classList.remove('active');
          });
          this.parentElement.classList.add('active');
        });
      });
    }

    function initPreviewUpdates() {
      console.log('Initializing preview updates');

      // Handle dimension changes
      const widthInput = document.querySelector('input[name="interface-width"]');
      const heightInput = document.querySelector('input[name="interface-height"]');
      const iframe = document.querySelector('.iframe-preview iframe');

      if (widthInput && iframe) {
        // Set initial width if not already set
        const currentWidth = parseInt(widthInput.value) || 900;
        iframe.style.width = currentWidth + 'px';
        iframe.setAttribute('width', currentWidth);
        
        widthInput.addEventListener('input', function() {
          const width = parseInt(this.value) || 900;
          iframe.style.width = width + 'px';
          iframe.setAttribute('width', width);
        });
      }

      if (heightInput && iframe) {
        heightInput.addEventListener('input', function() {
          const height = parseInt(this.value) || 500;
          if (height > 0) {
            iframe.style.height = height + 'px';
            iframe.setAttribute('height', height);
          }
        });
      }
    }

    // Utility function to load choices data
    function loadChoicesData(endpoint, targetElement, fieldName) {
      console.log('Loading choices for', fieldName, 'from', endpoint);

      const loadingHtml = '<div class="loading-state">Loading...</div>';
      targetElement.innerHTML = loadingHtml;

      fetch(endpoint, {
        method: 'GET',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        if (data && data.data && Array.isArray(data.data)) {
          renderChoices(targetElement, data.data, fieldName);
        } else {
          throw new Error('Invalid data format');
        }
      })
      .catch(error => {
        console.error('Error loading choices:', error);
        const errorHtml = '<div class="error-state">Sorry, list of ' + fieldName + ' is currently unavailable because of some server problems</div>';
        targetElement.innerHTML = errorHtml;
      });
    }

    function renderChoices(container, choices, fieldName) {
      console.log('Rendering', choices.length, 'choices for', fieldName);

      const cloudHtml = choices.map(choice => {
        const name = choice.name || choice.display || choice.slug;
        const slug = choice.slug;
        return '<span data-slug="' + slug + '"><a href="">' + name + '</a></span>';
      }).join('');

      container.innerHTML = cloudHtml;

      // Re-initialize tag functionality for this container
      initTagChoiceControls();
    }
  </script>
{% endblock content %}
