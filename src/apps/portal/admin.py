import csv

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import User
from django.contrib.flatpages.admin import FlatPageAdmin
from django.contrib.flatpages.models import FlatPage
from django.http import HttpResponse


from src.apps.portal.models import UserProfile


class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'Profile'
    readonly_fields = ('uuid',)


class PortalUserAdmin(UserAdmin):

    actions = ['export_as_csv']
    inlines = (UserProfileInline,)
    list_display = (
        'username', 'email',
        'first_name', 'last_name',
        'date_joined', 'last_login',
        'is_staff',
    )

    def export_as_csv(modeladmin, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="users.csv"'

        writer = csv.writer(response, dialect=csv.excel)
        for u in queryset.order_by('pk'):
            row = [u.pk, u.username, u.first_name, u.last_name, u.date_joined, u.is_active, u.is_staff]
            writer.writerow([el.encode('utf-8') if isinstance(el, str) else el for el in row])
        return response


# Re-register UserAdmin
admin.site.unregister(User)
admin.site.register(User, PortalUserAdmin)


class WsFlatPageAdmin(FlatPageAdmin):
    pass

admin.site.unregister(FlatPage)
admin.site.register(FlatPage, WsFlatPageAdmin)
