import json
import requests
from django import forms
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from .widgets import TurnstileWidget
from .validators import TurnstileValidator


class TurnstileField(forms.Field):
    """Custom Django form field for Cloudflare Turnstile validation"""
    
    widget = TurnstileWidget
    default_error_messages = {
        'required': _('Please complete the security verification.'),
        'invalid': _('Security verification failed. Please try again.'),
        'network_error': _('Security verification temporarily unavailable. Please try again.'),
        'expired': _('Security verification expired. Please refresh and try again.'),
    }
    
    def __init__(self, site_key=None, secret_key=None, theme='auto', size='normal', **kwargs):
        """
        Initialize Turnstile field
        
        Args:
            site_key: Turnstile site key (defaults to settings.TURNSTILE_SITE_KEY)
            secret_key: Turnstile secret key (defaults to settings.TURNSTILE_SECRET_KEY)
            theme: Theme ('light', 'dark', 'auto')
            size: Size ('normal', 'compact')
        """
        self.site_key = site_key or getattr(settings, 'TURNSTILE_SITE_KEY', None)
        self.secret_key = secret_key or getattr(settings, 'TURNSTILE_SECRET_KEY', None)
        self.theme = theme
        self.size = size
        
        # Don't require the field if Turnstile is disabled
        if not self.site_key or not self.secret_key:
            kwargs['required'] = False
            
        super().__init__(**kwargs)
        
        # Initialize validator
        self.validator = TurnstileValidator(
            site_key=self.site_key,
            secret_key=self.secret_key
        )
    
    def widget_attrs(self, widget):
        """Add Turnstile-specific attributes to widget"""
        attrs = super().widget_attrs(widget)
        attrs.update({
            'data-sitekey': self.site_key,
            'data-theme': self.theme,
            'data-size': self.size,
        })
        return attrs
    
    def validate(self, value):
        """Validate Turnstile response token"""
        super().validate(value)
        
        # Skip validation if Turnstile is not configured
        if not self.site_key or not self.secret_key:
            return
            
        if not value:
            if self.required:
                raise ValidationError(
                    self.error_messages['required'],
                    code='required'
                )
            return
            
        # Perform server-side validation
        try:
            is_valid, error_codes = self.validator.validate_response(value)
            
            if not is_valid:
                if 'timeout-or-duplicate' in error_codes:
                    raise ValidationError(
                        self.error_messages['expired'],
                        code='expired'
                    )
                else:
                    raise ValidationError(
                        self.error_messages['invalid'],
                        code='invalid'
                    )
                    
        except requests.RequestException:
            # Network error - log and handle gracefully
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("Turnstile validation network error", exc_info=True)
            
            # In production, might want to fail gracefully
            if getattr(settings, 'TURNSTILE_FAIL_SILENTLY', False):
                return
            else:
                raise ValidationError(
                    self.error_messages['network_error'],
                    code='network_error'
                )
    
    def has_changed(self, initial, data):
        """Turnstile responses are single-use, so always consider changed"""
        return True


class HoneypotField(forms.CharField):
    """Honeypot field to catch automated submissions"""
    
    def __init__(self, **kwargs):
        kwargs.setdefault('required', False)
        kwargs.setdefault('widget', forms.HiddenInput)
        super().__init__(**kwargs)
    
    def clean(self, value):
        """If honeypot field has value, it's likely a bot"""
        if value:
            # Log the attempt for monitoring
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Honeypot field triggered with value: {value}")
            
            # Silently reject by raising validation error
            raise ValidationError("Invalid submission", code='invalid')
        
        return value