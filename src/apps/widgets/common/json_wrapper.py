import json
from collections.abc import Items<PERSON>iew, ValuesView, KeysView


class Default<PERSON>son(object):
    """
    This is like dict with preliminary setdefault for all fields,
    but for any json object.

    Assume that you have some json default_instance and
    some json primary_instance, which is subset of default_instance.
    Use obj = DefaultJson(primary_instance, default_instance).
    Then just get some value from obj:
    obj['property1']['property2']['property3']

    If path of these properties doesn't exist in primary_instance,
    value is retrieved from default_instance.
    """

    def __init__(self, primary_instance, default_instance=None):
        self.primary, self.default = primary_instance, default_instance

    def _get(self, instance, item_tuple, wrap=True):
        # If we receive a nested DefaultJson wrapper, unwrap it to its
        # underlying *primary* object so we don’t accidentally recurse
        # indefinitely (or attempt to apply the full key-tuple to the wrapper
        # again, which can raise spurious KeyErrors like ``0`` when the first
        # element in the tuple is an integer index).
        result = instance.primary if isinstance(instance, Default<PERSON>son) else instance

        if not isinstance(item_tuple, (tuple, list)):
            item_tuple = (item_tuple,)

        for item in item_tuple:
            try:
                result = result[item]
            except (TypeError, KeyError, IndexError):
                # DEBUG: log problematic access once to help trace origin
                import logging, traceback
                logger = logging.getLogger(__name__)
                logger.warning(
                    "DefaultJson _get failed: item=%s (%s) on object type %s. Path=%s",
                    item,
                    type(item).__name__,
                    type(result).__name__,
                    item_tuple,
                )
                # Graceful fallback: if the current result is a dict and the
                # key is int 0/1 etc, keep raising KeyError so caller falls
                # back to default; otherwise if result is list/tuple and item
                # is int but out of range, raise IndexError.
                raise KeyError(item)

        if wrap and isinstance(result, (dict, DefaultJson)):
            sub_default = None
            if instance == self.primary and self.default is not None:
                sub_default = self.get(*item_tuple,
                                       get_from_default=True)
            return DefaultJson(result, sub_default)
        return result

    def __getitem__(self, item_tuple):
        if self.default is None:
            return self._get(self.primary, item_tuple)

        try:
            return self._get(self.primary, item_tuple)
        except (KeyError, IndexError):
            return self._get(self.default, item_tuple)

    def get(self, *args, **kwargs):
        if kwargs.get('get_from_default', False):
            instance = self.default
        else:
            instance = self.primary
        try:
            return self._get(instance, args, wrap=False)
        except (KeyError, IndexError):
            return kwargs.get('default', None)

    def keys(self):
        # Add safety check for None
        if self.default is not None:
            return self.default.keys()
        else:
            return []

    # Provide a proper iterator so that Python / Django templates don’t fall
    # back to numeric indexing (__getitem__(0), __getitem__(1)…).
    def __iter__(self):
        """Iterate over the union of primary and default keys."""
        if isinstance(self.primary, dict):
            # Start with primary keys so overridden values come first.
            seen = set()
            for key in self.primary.keys():
                seen.add(key)
                yield key
            # Then yield any additional keys from default.
            if isinstance(self.default, dict):
                for key in self.default.keys():
                    if key not in seen:
                        yield key
        elif isinstance(self.default, dict):
            # No primary dict, fall back to default’s keys.
            yield from self.default.keys()
        else:
            # Not a mapping – behave like an empty iterator.
            return

    def __len__(self):
        """Return the number of unique keys available for mapping behaviour."""
        return len(list(iter(self)))

    def iterkeys(self):
        # Add safety check for None and use Python 3 native keys()
        if self.default is not None:
            return iter(self.default.keys())
        else:
            return iter([])

    class JsonEncoder(json.JSONEncoder):
        def default(self, o):
            if isinstance(o, DefaultJson):
                # Add safety check for None and use Python 3 native keys()
                if o.default is not None:
                    return {key: o[key] for key in o.default.keys()}
                else:
                    return {}
            if isinstance(o, (KeysView, ValuesView, ItemsView)):
                return list(o)

            return super(DefaultJson.JsonEncoder, self).default(o)


class WidgetParamsJsonWrapper(DefaultJson):

    @property
    def width(self):
        return self['interface']['dimensions']['width']

    @property
    def height(self):
        return self['interface']['dimensions']['height']

    @property
    def active_theme(self):
        return self['theme']['active']
