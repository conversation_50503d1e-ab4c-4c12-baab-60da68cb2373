from src.apps.widgets.common.forms.main import FakeModelForm
from src.apps.widgets.common.fields import WsJsonFormField


class WidgetPermissionsForm(FakeModelForm):
    domains = WsJsonFormField(required=True)

    def decompose_to_initial(self):
        """Return initial domains list, falling back to widget_type default config
        if the current instance does not contain the key.  This prevents KeyError
        on newly-created widgets whose raw params may not yet include the
        `permissions` section (e.g. legacy finder-v2 defaults).
        """

        domains = None

        # Primary source – current instance dict (may be empty)
        try:
            domains = self.instance['domains']
        except (KeyError, TypeError):
            pass

        # Fallback – widget type default config if available
        if domains is None and getattr(self, 'widget_type', None) is not None:
            default_permissions = self.widget_type.default_config.get('permissions', {})
            domains = default_permissions.get('domains', [])

        # As a last resort, use empty list to keep the form functional
        if domains is None:
            domains = []

        return {
            'domains': domains
        }

    def compose_to_save(self, data):
        return {
            'domains': data['domains']
        }


