# Using ws-django-betterforms v2.0.0 - WS-maintained Django 4.2 compatible fork
from betterforms.multiform import MultiModel<PERSON>orm
from django import forms

from src.apps.widgets.common.json_wrapper import Default<PERSON><PERSON>
from src.apps.widgets.common.models import WidgetConfig


class FakeModelForm(forms.Form):

    use_default_json = True

    def __init__(self, *args, **kwargs):
        self.instance = kwargs.pop('instance', None)
        # When used inside MultiModelForm, plain (non-Model) forms often
        # receive their pre-populated data via the ``initial`` keyword
        # argument instead of the ``instance`` keyword that ModelForms
        # rely on.  Finder-v2’s ContentFilterForm expects the stored JSON
        # section in ``self.instance`` so it can decompose it into field
        # values.  If MultiModelForm passed us no explicit ``instance``
        # (it will be ``None``) but did supply an ``initial`` mapping,
        # promote that mapping to ``self.instance``.
        if self.instance is None and 'initial' in kwargs:
            possible_instance = kwargs['initial']
            # Accept plain dict or DefaultJson wrapper as a valid source.
            if isinstance(possible_instance, (dict, DefaultJson)):
                self.instance = possible_instance

            # Optional: quick debug statement (kept lightweight – INFO
            # level so it can be silenced in production).
            import logging, threading
            logger = logging.getLogger(__name__)
            thread_id = threading.get_ident()
            logger.info(
                f"🎯 FAKE_MODEL_FORM [Thread {thread_id}]: Promoted 'initial' to 'instance' for {self.__class__.__name__} – type: {type(self.instance)}"
            )
        self.widget_type = kwargs.pop('widget_type', None)
        kwargs.pop('widget', None)  # Pop the widget kwarg to avoid passing it to parent
        super(FakeModelForm, self).__init__(*args, **kwargs)
        
        # DEBUG: Log decompose_to_initial call
        import logging
        import threading
        logger = logging.getLogger(__name__)
        thread_id = threading.get_ident()
        
        form_class_name = self.__class__.__name__
        logger.debug(f"🎯 FAKE_MODEL_FORM [Thread {thread_id}]: {form_class_name}.__init__ calling decompose_to_initial()")
        logger.debug(f"🎯 FAKE_MODEL_FORM [Thread {thread_id}]: {form_class_name} instance: {type(self.instance)} - {str(self.instance)[:200] if self.instance else 'None'}")
        
        initial_data = self.decompose_to_initial()
        logger.debug(f"🎯 FAKE_MODEL_FORM [Thread {thread_id}]: {form_class_name}.decompose_to_initial() returned: {initial_data}")
        logger.debug(f"🎯 FAKE_MODEL_FORM [Thread {thread_id}]: {form_class_name} initial before update: {self.initial}")
        
        self.initial.update(initial_data)
        
        logger.debug(f"🎯 FAKE_MODEL_FORM [Thread {thread_id}]: {form_class_name} initial after update: {self.initial}")

    def compose_to_save(self, data):
        return data

    def decompose_to_initial(self):
        return {}

    def save(self, commit=True):
        if self.use_default_json:
            data = DefaultJson(self.cleaned_data, self.initial)
        else:
            data = self.cleaned_data
        return self.compose_to_save(data)


class WidgetConfigForm(MultiModelForm):

    def __init__(self, *args, **kwargs):
        import logging
        import threading
        logger = logging.getLogger(__name__)

        thread_id = threading.get_ident()

        logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: WidgetConfigForm.__init__ called")
        logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: args length: {len(args)}")
        logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: kwargs keys: {list(kwargs.keys())}")

        # Log POST data if present
        if args and len(args) > 0:
            data = args[0]
            logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: POST data type: {type(data)}")
            if hasattr(data, 'items'):
                post_data = dict(data)
                logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: POST data keys: {list(post_data.keys())}")
                if 'content-regions' in post_data:
                    logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: POST content-regions: {post_data['content-regions']} (type: {type(post_data['content-regions'])})")

        self.widget = kwargs.pop('widget', None)
        # Extract request_user for widget creation (ensures authenticated users get ownership)
        self.request_user = kwargs.pop('request_user', None)
        config = kwargs.get('instance')
        self.widget_type = config.widget_type
        kwargs['instance'] = self.prepare_instances(config)

        logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: About to call super().__init__")
        logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: form_classes: {list(self.form_classes.keys())}")

        super(WidgetConfigForm, self).__init__(*args, **kwargs)

        logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: WidgetConfigForm.__init__ completed")

        # Log individual form data after initialization
        for form_name, form_instance in self.forms.items():
            if hasattr(form_instance, 'data') and form_instance.data:
                logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: {form_name} form data: {dict(form_instance.data) if form_instance.data else 'No data'}")
                if form_name == 'content' and 'content-regions' in form_instance.data:
                    logger.debug(f"🔍 MULTI FORM INIT [Thread {thread_id}]: {form_name} content-regions: {form_instance.data['content-regions']}")

    def get_form_args_kwargs(self, *args, **kwargs):
        import logging
        logger = logging.getLogger(__name__)
        
        fargs, fkwargs = super().get_form_args_kwargs(*args, **kwargs)
        fkwargs['widget_type'] = self.widget_type

        # For the theme sub-form give it the *instance* being edited,
        # not the global default config.
        if isinstance(fkwargs.get('instance'), WidgetConfig):
            logger.debug(f"🔧 FORM ARGS: Setting widget from instance (WidgetConfig type: {fkwargs['instance'].type})")
            fkwargs['widget'] = fkwargs['instance']   # ← use the new/actual config
        else:
            # For theme forms and other forms that need the widget config,
            # provide the config instance from our instances dict
            config_instance = getattr(self, 'instances', {}).get('config')
            if config_instance:
                logger.debug(f"🔧 FORM ARGS: Setting widget from instances dict (type: {config_instance.type})")
                fkwargs['widget'] = config_instance
            else:
                logger.debug(f"🔧 FORM ARGS: No config instance found in instances dict")
        
        widget = fkwargs.get('widget')
        if widget:
            logger.debug(f"🔧 FORM ARGS: Final widget type: {widget.type}")
        else:
            logger.debug(f"🔧 FORM ARGS: No widget set")
        return fargs, fkwargs

    def prepare_instances(self, config):
        import logging
        logger = logging.getLogger(__name__)
        
        # CRITICAL FIX: Use request_user for new widgets instead of copying None from default config
        new_widget_user = self.request_user if (config.is_default and self.request_user and self.request_user.is_authenticated) else config.user        
        instances = {
            'config': config if not config.is_default else WidgetConfig(
                type=config.type,
                user=new_widget_user),
        }
        for key, form_class in self.form_classes.items():
            if key != 'config':
                # Special handling for theme forms - they manage their own data from database
                if key == 'theme':
                    instances[key] = None  # Theme form will get widget_config and load theme from DB
                else:
                    # Ensure we propagate DefaultJson wrappers to forms that
                    # declare ``use_default_json = True``.  Accessing
                    # `config.params[key]` (item lookup) preserves the
                    # wrapper, whereas calling ``.get()`` returns the raw
                    # underlying dict and strips away DefaultJson behaviour
                    # (including the customised ``get(default=…)`` signature
                    # that ContentFilterForm relies on).
                    if getattr(form_class, 'use_default_json', False):
                        try:
                            value = config.params[key]  # keep DefaultJson
                        except Exception as exc:
                            # Fallback to empty dict if the section is missing
                            value = {}
                            import logging, threading
                            logger = logging.getLogger(__name__)
                            logger.warning(
                                "🎯 PREPARE_INSTANCES: Missing JSON section '%s' – created empty DefaultJson (%s)" % (key, exc)
                            )
                    else:
                        value = config.params.get(key)
                    if value is None:
                        value = {}
                    instances[key] = value
        return instances

    def prepare_to_save(self, config, objects):
        result = {}
        for key, form_class in self.form_classes.items():
            if key != 'config':
                obj = objects.get(key, None)
                if obj is not None:
                    # Special handling for theme form - it manages its own saving
                    if key == 'theme':
                        result[key] = obj  # Theme form doesn't use compose_to_save pattern
                        continue
                    
                    # CRITICAL FIX: Do not call compose_to_save again! The obj is already
                    # the result of compose_to_save from subform.save(). Calling it twice
                    # causes data loss (e.g., output_template gets cleared because the
                    # composed dict doesn't have the expected cleaned_data keys).
                    # Just use the already-processed obj directly.
                    result[key] = obj
        result.update(self.widget_type.INTERNAL_META_DATA)
        return result

    def save(self, commit=True):
        objects = super(WidgetConfigForm, self).save(commit=False)
        config, subscription = objects['config']
        config.raw_params = self.prepare_to_save(config, objects)
        config.save()
        subscription.widget_config = config
        subscription.save()
        
        # CRITICAL FIX: Handle theme saving after config is saved
        if 'theme' in self.forms and hasattr(self.forms['theme'], 'save_theme_after_config'):
            theme_form = self.forms['theme']
            theme_form.save_theme_after_config(config)        
        return objects
