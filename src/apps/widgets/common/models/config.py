import uuid

from django.conf import settings
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.db import models, DataError
from django.http import Http404
from django.urls import reverse
from django.utils.functional import cached_property

from src.apps.widgets.common.exceptions import Widget404
from src.apps.widgets.common.fields import Always<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from src.apps.widgets.widget_type import WidgetTypeManager, WidgetType


class WidgetConfig(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL,
                             on_delete=models.SET_NULL,
                             null=True,
                             related_name='widgets')
    uuid = AlwaysHexUUIDField(default=uuid.uuid4, primary_key=True)

    is_default = models.BooleanField('Default Config', default=False)

    name = models.CharField(max_length=255)

    # choices=WidgetType.choices
    type = models.CharField(max_length=20)
    lang = models.Char<PERSON>ield(max_length=10, default='en')

    raw_params = J<PERSON><PERSON>ield(default=dict)

    # automatic fields
    created_at = models.DateTimeField('Creation date', auto_now_add=True)
    updated_at = models.DateTimeField('Update date', auto_now=True)

    objects = WidgetTypeManager()

    def __str__(self):
        if self.is_default:
            return '%s - Default' % self.type
        try:
            if self.subscription.is_demo:
                return '%s [%s] - demo - %s' % (self.type,
                                                str(self.uuid)[:6],
                                                self.name)
            if self.subscription.is_trial:
                return '%s [%s] - trial' % (self.type, str(self.uuid)[:6])
        except:
            # Subscription not loaded, provide fallback representation
            return '%s [%s] - %s' % (self.type, str(self.uuid)[:6], self.name)
        return '%s [%s] - %s - %s' % (self.type, str(self.uuid)[:6],
                                      self.user, self.name)

    def get_absolute_url(self):
        try:
            if self.subscription.is_trial:
                view_name = 'widget:configure-demo'  # Use demo endpoint instead of removed try endpoint
            else:
                view_name = 'widget:configure'
        except:
            # Subscription not loaded, default to regular configure
            view_name = 'widget:configure'
        return reverse(view_name, kwargs=dict(widget_slug=self.slug))

    def get_iframe_url(self):
        return reverse('widget:iframe', kwargs=dict(widget_slug=self.slug))

    def get_test_url(self):
        return reverse('widget-embed:test', kwargs=dict(widget_slug=self.slug))

    @cached_property
    def params(self):
        return self.widget_type.json_wrapper(self.raw_params,
                                             self.widget_type.default_config)

    @cached_property
    def widget_type(self):
        return WidgetType.get(self.type)

    @cached_property
    def language(self):
        from src.apps.widgets.translation.models import WidgetTranslation
        try:
            return WidgetTranslation.objects.get(slug=self.lang, type=self.type)
        except WidgetTranslation.DoesNotExist:
            raise Http404

    @classmethod
    def get_default(cls, widget_type, default=Http404):
        try:
            return cls.objects.select_related('subscription').get(is_default=True, type=widget_type)
        except cls.DoesNotExist:
            if isinstance(default, Exception):
                raise default
            return default

    @classmethod
    def get(cls, uuid):
        try:
            return cls.objects.select_related('subscription').get(uuid=uuid)
        except (cls.DoesNotExist, DataError):
            raise Widget404(message='Widget Not Found')

    @cached_property
    def slug(self):
        if self.is_default:
            return self.type
        if isinstance(self.uuid, uuid.UUID):
            return self.uuid.hex
        return self.uuid
