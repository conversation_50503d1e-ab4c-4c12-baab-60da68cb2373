from django.urls import re_path, include
from django.contrib.auth.decorators import login_required

from src.apps.widgets.widget_type import WidgetType
from src.apps.widgets.main import views


# Widget URL patterns


widget_urlpatterns = [
    re_path(r'^config/$',
            login_required(views.WidgetConfigView.as_view()),
            name='configure'),
    re_path(r'^config-demo/$',
            views.WidgetDemoConfigView.as_view(),
            name='configure-demo'),
    re_path(r'^delete/$',
            views.delete_widget,
            name='delete'),
    re_path(r'^$', views.WidgetView.as_view(), name='iframe'),
]

def create_finder_v2_default(request):
    """
    Temporary view to create finder-v2 default configuration.
    This is a helper endpoint to set up the default configuration.
    """
    from django.http import JsonResponse
    from src.apps.widgets.common.models import WidgetConfig, WidgetSubscription

    try:
        # Check if default config already exists
        existing_config = WidgetConfig.objects.filter(type='finder-v2', is_default=True).first()
        if existing_config:
            return JsonResponse({
                'status': 'exists',
                'message': f'Default configuration already exists: {existing_config.uuid}',
                'uuid': existing_config.uuid
            })

        # Create default configuration
        config = WidgetConfig(
            user=None,
            name='Finder-v2 Default',
            is_default=True,
            type='finder-v2',
            lang='en',
            raw_params={
                "flow_type": "primary",
                "theme": {
                    "active": {
                        "templates": {
                            "page": ["widgets/finder_v2/iframe/page.html"]
                        }
                    }
                },
                "interface_tabs": {
                    "tabs": {
                        "by_vehicle": {"resource": "by_vehicle"},
                        "by_tire": {"resource": "by_tire"},
                        "by_rim": {"resource": "by_rim"}
                    },
                    "primary": {"resource": "by_vehicle"}
                },
                "interface_blocks": {
                    "button_to_ws": {"hide": False}
                },
                "content": {
                    "markets": []
                },
                "permissions": {
                    "domains": [
                        "localhost",
                        "development.local",
                        "127.0.0.1",
                        "*.localhost",
                        "*.development.local"
                    ]
                }
            }
        )
        config.save()

        # Ensure the default configuration has a theme attached so that
        # the preview iframe can pick up proper CSS variables.  Without a
        # stored theme `render_theme_css` is skipped and the widget falls
        # back to un-themed Tailwind defaults (appearing as “Corporate Gray”).
        # We create the theme only if one does not already exist.
        try:
            from src.apps.widgets.finder_v2.default_config.predefined_themes import PredefinedThemes
            if not hasattr(config, 'theme'):
                theme = PredefinedThemes.create_widget_theme(
                    widget_config=config,
                    theme_name=PredefinedThemes.DEFAULT_THEME,   # Modern Blue
                )
                theme.save()
        except Exception as e:
            # Log but do not interrupt default-config creation; theme can be
            # added manually later if this fails for any reason.
            import logging
            logging.getLogger(__name__).error(
                "Failed to attach default theme to finder-v2 default config: %s",
                e,
            )

        # Create subscription
        subscription = WidgetSubscription(
            widget_config=config,
            contact_email='<EMAIL>',
            client_name='Default Configuration',
            notes='Default configuration for finder-v2 widget'
        )
        subscription.save()

        return JsonResponse({
            'status': 'created',
            'message': 'Finder-v2 default configuration created successfully!',
            'uuid': config.uuid,
            'public_url': 'http://development.local:8000/widget/finder-v2/config-demo/'
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error creating default configuration: {str(e)}'
        }, status=500)


urlpatterns = [
    # Temporary helper endpoint to create finder-v2 default configuration
    re_path(r'^setup-finder-v2-default/$', create_finder_v2_default, name='setup-finder-v2-default'),

    # Standard widget URL patterns - finder-v2 simplified routing
    # /widget/finder-v2/config/ -> WidgetConfigView (login required)
    # /widget/finder-v2/config-demo/ -> WidgetDemoConfigView (public access)
    re_path(r'^%s/' % WidgetType.slug_url_pattern, include(widget_urlpatterns)),
    re_path(r'^%s$' % WidgetType.slug_url_pattern, views.WidgetView.as_view()),
]


