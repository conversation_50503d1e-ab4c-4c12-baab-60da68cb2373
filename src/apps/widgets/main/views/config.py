from django.urls import reverse
from django.http import Http404
from django.views.generic.edit import UpdateView
from django.contrib import messages
from django.utils.translation import gettext as _

from src.apps.widgets.common.models import WidgetConfig


class WidgetConfigView(UpdateView):

    def form_invalid(self, form):
        """Override to add debugging for form validation errors."""
        import logging
        import threading
        logger = logging.getLogger(__name__)

        thread_id = threading.get_ident()

        logger.error(f"🔍 VIEW INVALID [Thread {thread_id}]: Config form validation failed: {form.errors}")

        # Log individual form errors with detailed debugging
        for form_name, subform in form.forms.items():
            if not subform.is_valid():
                logger.error(f"🔍 VIEW INVALID [Thread {thread_id}]:   {form_name} form errors: {subform.errors}")

                # Special debugging for ContentFilterForm
                if form_name == 'content' and hasattr(subform, 'data'):
                    logger.error(f"🔍 VIEW INVALID [Thread {thread_id}]:   {form_name} form raw data: {dict(subform.data) if subform.data else 'No data'}")

                    # Check for specific regions field error
                    if 'regions' in subform.errors:
                        logger.error(f"🔍 VIEW INVALID [Thread {thread_id}]:   REGIONS FIELD ERROR DETECTED")
                        if 'content-regions' in subform.data:
                            raw_regions = subform.data['content-regions']
                            logger.error(f"🔍 VIEW INVALID [Thread {thread_id}]:   Raw content-regions data: {raw_regions} (type: {type(raw_regions)})")

        return super().form_invalid(form)
    model = WidgetConfig

    def __init__(self, **kwargs):
        super(WidgetConfigView, self).__init__(**kwargs)
        self.admin_mode = False

    def get_form_class(self):
        return self.object.widget_type.forms['config']

    def get_template_names(self):
        return self.object.widget_type.templates['config']

    def user_has_permission(self):
        return self.request.user.has_perm('widgets.can_edit')

    def get_object(self, queryset=None):
        config = self.request.config

        if not config:
            # Check if this is a finder-v2 config request
            widget_slug = self.kwargs.get('widget_slug')
            if widget_slug == 'finder-v2':
                # Since finder-v2 is always enabled, allow access
                # to its default config without special checks
                try:
                    config = WidgetConfig.objects.select_related('subscription').get(
                        type=widget_slug, is_default=True
                    )
                except WidgetConfig.DoesNotExist:
                    raise Http404
            else:
                raise Http404

        return config

    def get_form_kwargs(self):
        import logging
        import threading
        logger = logging.getLogger(__name__)

        thread_id = threading.get_ident()

        kwargs = super(WidgetConfigView, self).get_form_kwargs()
        kwargs['widget'] = self.object
        # Pass request user so form can assign new widgets to authenticated users
        kwargs['request_user'] = self.request.user

        logger.debug(f"🔍 VIEW KWARGS [Thread {thread_id}]: get_form_kwargs called")
        logger.debug(f"🔍 VIEW KWARGS [Thread {thread_id}]: kwargs keys: {list(kwargs.keys())}")

        # Log POST data if present (only in DEBUG mode)
        from django.conf import settings
        if settings.DEBUG and 'data' in kwargs and kwargs['data']:
            logger.debug(f"POST data received with {len(kwargs['data'])} fields")
            if hasattr(kwargs['data'], 'items'):
                post_data = dict(kwargs['data'])
                # Only log specific problematic fields
                if 'content-regions' in post_data:
                    logger.debug(f"POST content-regions: {post_data['content-regions']}")

        return kwargs

    def form_valid(self, form):
        """
        Override form_valid to handle custom form saving and object updating.

        Our WidgetConfigForm doesn't follow Django's standard ModelForm pattern,
        so we need to handle the saving manually and update self.object.
        """
        import logging
        logger = logging.getLogger(__name__)
        # Save the form manually (don't let Django's UpdateView do it)
        saved_objects = form.save()
        logger.info(f"Form saved successfully. Objects: {saved_objects.keys()}")

        # Update self.object to point to the newly saved config
        if 'config' in saved_objects:
            config, subscription = saved_objects['config']
            logger.info(f"Updating object from {self.object.slug} to {config.slug}")

            # CRITICAL FIX: Reload the config from database to clear cached properties
            # The params property is cached, so we need fresh data for form initialization
            self.object = WidgetConfig.objects.get(uuid=config.uuid)

            # Clear any cached properties to ensure fresh data
            if hasattr(self.object, '_params'):
                delattr(self.object, '_params')
            if hasattr(self.object, '_widget_type'):
                delattr(self.object, '_widget_type')

        # Add success message
        messages.success(self.request, _('Configuration widget has been saved successfully'))

        # Skip Django's form.save() by calling the parent's parent method
        from django.views.generic.edit import FormMixin
        return FormMixin.form_valid(self, form)

    def get_success_url(self):
        try:
            return reverse('widget:configure',
                           kwargs={'widget_slug': self.object.slug})
        except Exception as e:
            # Fallback to same page if reverse fails
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in get_success_url for config: {e}")
            return self.request.path


class TryWidgetView(WidgetConfigView):
    """
    Try widget view that displays the configuration interface for testing.
    
    This allows users to try widget configurations without requiring authentication.
    Uses the widget's 'try' form which is typically the same as config form but
    with different permissions.
    """

    def get_form_class(self):
        return self.object.widget_type.forms['try']

    def get_success_url(self):
        return reverse('widget:configure-demo',
                       kwargs=dict(widget_slug=self.object.slug))

    def get_object(self, queryset=None):
        config = self.request.config

        # Check if config is None or an exception (no default configuration exists)
        if config is None or isinstance(config, Exception):
            raise Http404("Widget configuration not found")

        # For try mode, use default config and don't require user authentication
        if config.is_default:
            config.user = None  # Allow anonymous access
        elif config.subscription.deny_try():
            raise Http404("Try mode not available for this widget")

        return config


class WidgetDemoConfigView(WidgetConfigView):

    def form_invalid(self, form):
        """Override to add debugging for form validation errors."""
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Demo form validation failed: {form.errors}")

        # Log individual form errors
        for form_name, subform in form.forms.items():
            if not subform.is_valid():
                logger.error(f"  {form_name} form errors: {subform.errors}")

        return super().form_invalid(form)
    """
    Demo configuration view with public access (no authentication required).

    This replaces the /widget/finder-v2/try/ endpoint functionality by providing
    public access to widget configuration. Uses the same unified template as
    the authenticated config endpoint but allows anonymous users.
    """

    def get_form_class(self):
        return self.object.widget_type.forms['demo']

    def get_template_names(self):
        return self.object.widget_type.templates['demo']

    def form_valid(self, form):
        """
        Override form_valid to handle custom form saving and object updating.

        Our WidgetConfigForm doesn't follow Django's standard ModelForm pattern,
        so we need to handle the saving manually and update self.object.
        """
        # Save the form manually (don't let Django's UpdateView do it)
        saved_objects = form.save()

        # Update self.object to point to the newly saved config
        if 'config' in saved_objects:
            config, subscription = saved_objects['config']

            # CRITICAL FIX: Reload the config from database to clear cached properties
            # The params property is cached, so we need fresh data for form initialization
            from src.apps.widgets.common.models import WidgetConfig
            self.object = WidgetConfig.objects.get(uuid=config.uuid)

            # Clear any cached properties to ensure fresh data
            if hasattr(self.object, '_params'):
                delattr(self.object, '_params')
            if hasattr(self.object, '_widget_type'):
                delattr(self.object, '_widget_type')

        # Add success message
        messages.success(self.request, _('Configuration widget has been saved successfully'))

        # Debug logging to verify form_valid is called
        import logging
        logger = logging.getLogger(__name__)
        logger.info("SUCCESS MESSAGE ADDED (DEMO): Configuration widget has been saved successfully")

        # Skip Django's form.save() by calling the parent's parent method
        from django.views.generic.edit import FormMixin
        return FormMixin.form_valid(self, form)

    def get_success_url(self):
        try:
            return reverse('widget:configure-demo',
                           kwargs=dict(widget_slug=self.object.slug))
        except Exception as e:
            # Fallback to same page if reverse fails
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in get_success_url for demo config: {e}")
            return self.request.path

    def user_has_permission(self):
        # Override to allow public access without authentication
        return True

    def get_object(self):
        config = self.request.config

        # PUBLIC ACCESS: Allow demo configuration without authentication
        # This makes config-demo endpoint publicly accessible like the try endpoint
        self.admin_mode = False  # Always false for public demo access

        if config.is_default:
            config.user = None  # Allow anonymous access to default configuration
        elif config.subscription.deny_demo():
            raise Http404("Demo access not available for this widget")

        return config








