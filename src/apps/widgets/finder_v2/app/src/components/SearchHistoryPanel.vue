<template>
  <div v-if="shouldShow" class="search-history-panel">
    <!-- Panel Header -->
    <div class="panel-header" @click="toggleExpanded">
      <h3 class="panel-title">Recent Searches</h3>
      <span 
        class="expand-icon"
        :class="{ 'expanded': isExpanded }"
        aria-hidden="true"
      >
        ▼
      </span>
    </div>

    <!-- Panel Content -->
    <transition name="history-expand">
      <div v-if="isExpanded" class="panel-content">
        <!-- Search History List -->
        <div v-if="displaySearches.length > 0" class="search-list">
          <div
            v-for="search in visibleSearches"
            :key="search.id"
            class="search-item"
            @click="executeSearch(search.id)"
            @keydown.enter="executeSearch(search.id)"
            @keydown.space.prevent="executeSearch(search.id)"
            tabindex="0"
            role="button"
            :aria-label="`Execute search for ${search.description}`"
          >
            <div class="search-content">
              <div class="search-description">{{ search.description }}</div>
              <div class="search-time">{{ getRelativeTime(search.timestamp) }}</div>
            </div>
            <button
              class="remove-button"
              @click.stop="removeSearch(search.id)"
              @keydown.enter.stop="removeSearch(search.id)"
              @keydown.space.stop.prevent="removeSearch(search.id)"
              :aria-label="`Remove search for ${search.description}`"
              title="Remove this search"
            >
              ✕
            </button>
          </div>
        </div>

        <!-- Show More Button -->
        <div v-if="hasMoreSearches && !showAll" class="show-more-container">
          <button
            class="show-more-button"
            @click="showAll = true"
            @keydown.enter="showAll = true"
            @keydown.space.prevent="showAll = true"
            aria-label="Show more search history"
          >
            Show More ({{ remainingCount }} more)
          </button>
        </div>

        <!-- Show Less Button -->
        <div v-if="showAll && hasMoreSearches" class="show-less-container">
          <button
            class="show-less-button"
            @click="showAll = false"
            @keydown.enter="showAll = false"
            @keydown.space.prevent="showAll = false"
            aria-label="Show less search history"
          >
            Show Less
          </button>
        </div>

        <!-- Empty State -->
        <div v-if="displaySearches.length === 0" class="empty-state">
          <p class="empty-message">No recent searches found.</p>
        </div>

        <!-- Clear All Button -->
        <div v-if="displaySearches.length > 0" class="clear-all-container">
          <button
            class="clear-all-button"
            @click="confirmClearAll"
            @keydown.enter="confirmClearAll"
            @keydown.space.prevent="confirmClearAll"
            aria-label="Clear all search history"
          >
            Clear All History
          </button>
        </div>
      </div>
    </transition>

    <!-- Clear Confirmation Modal -->
    <div v-if="showClearConfirmation" class="confirmation-overlay" @click="cancelClearAll">
      <div class="confirmation-modal" @click.stop>
        <h4 class="confirmation-title">Clear Search History</h4>
        <p class="confirmation-message">
          Are you sure you want to clear all your search history? This action cannot be undone.
        </p>
        <div class="confirmation-buttons">
          <button
            class="confirm-button"
            @click="clearAllHistory"
            @keydown.enter="clearAllHistory"
            @keydown.space.prevent="clearAllHistory"
          >
            Clear All
          </button>
          <button
            class="cancel-button"
            @click="cancelClearAll"
            @keydown.enter="cancelClearAll"
            @keydown.space.prevent="cancelClearAll"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/finder'

export default {
  name: 'SearchHistoryPanel',
  setup() {
    const finderStore = useFinderStore()
    const { config } = storeToRefs(finderStore)
    
    // Component state
    const isExpanded = ref(false)
    const showAll = ref(false)
    const showClearConfirmation = ref(false)
    const searchHistory = ref(null)
    
    // Initialize search history
    onMounted(() => {
      searchHistory.value = finderStore.getSearchHistory()
    })
    
    // Watch for config changes to reinitialize search history
    watch(() => config.value, () => {
      searchHistory.value = finderStore.getSearchHistory()
    }, { deep: true })
    
    // Computed properties
    const isEnabled = computed(() => {
      return searchHistory.value?.isEnabled.value ?? false
    })
    
    const displaySearches = computed(() => {
      return searchHistory.value?.displaySearches.value ?? []
    })
    
    const allSearches = computed(() => {
      return searchHistory.value?.searches.value ?? []
    })
    
    const hasMoreSearches = computed(() => {
      return searchHistory.value?.hasMoreSearches.value ?? false
    })
    
    const shouldShow = computed(() => {
      return isEnabled.value && displaySearches.value.length > 0
    })
    
    const visibleSearches = computed(() => {
      if (showAll.value) {
        return allSearches.value
      }
      return displaySearches.value
    })
    
    const remainingCount = computed(() => {
      return Math.max(0, allSearches.value.length - displaySearches.value.length)
    })
    
    // Auto-expand configuration
    const autoExpand = computed(() => {
      const historyConfig = config.value?.search_history || {}
      return historyConfig.autoExpand === true
    })
    
    // Watch for auto-expand setting
    watch([shouldShow, autoExpand], ([show, expand]) => {
      if (show && expand) {
        isExpanded.value = true
      }
    }, { immediate: true })
    
    // Methods
    function toggleExpanded() {
      isExpanded.value = !isExpanded.value
    }
    
    async function executeSearch(searchId) {
      try {
        await finderStore.executeSearchFromHistory(searchId)
        
        // Update search history reference after execution
        searchHistory.value = finderStore.getSearchHistory()
        
        // Scroll to results if available
        setTimeout(() => {
          const resultsElement = document.querySelector('.results-container')
          if (resultsElement) {
            resultsElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
          }
        }, 100)
        
      } catch (error) {
        console.error('Failed to execute search from history:', error)
      }
    }
    
    function removeSearch(searchId) {
      if (searchHistory.value) {
        searchHistory.value.removeSearch(searchId)
        
        // If no searches left, collapse panel
        if (displaySearches.value.length === 0) {
          isExpanded.value = false
          showAll.value = false
        }
      }
    }
    
    function confirmClearAll() {
      showClearConfirmation.value = true
    }
    
    function clearAllHistory() {
      if (searchHistory.value) {
        searchHistory.value.clearHistory()
        showClearConfirmation.value = false
        isExpanded.value = false
        showAll.value = false
      }
    }
    
    function cancelClearAll() {
      showClearConfirmation.value = false
    }
    
    function getRelativeTime(timestamp) {
      return searchHistory.value?.getRelativeTime(timestamp) || ''
    }
    
    // Keyboard navigation
    function handleKeydown(event) {
      if (event.key === 'Escape' && showClearConfirmation.value) {
        cancelClearAll()
        event.preventDefault()
      }
    }
    
    // Add global keyboard listener
    onMounted(() => {
      document.addEventListener('keydown', handleKeydown)
    })
    
    // Clean up listener
    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleKeydown)
    })
    
    return {
      isExpanded,
      showAll,
      showClearConfirmation,
      shouldShow,
      displaySearches,
      allSearches,
      hasMoreSearches,
      visibleSearches,
      remainingCount,
      toggleExpanded,
      executeSearch,
      removeSearch,
      confirmClearAll,
      clearAllHistory,
      cancelClearAll,
      getRelativeTime
    }
  }
}
</script>

<style scoped>
.search-history-panel {
  margin-bottom: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #ffffff;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.15s ease;
}

.panel-header:hover {
  background-color: #f3f4f6;
}

.panel-title {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.expand-icon {
  font-size: 0.75rem;
  color: #6b7280;
  transition: transform 0.15s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.panel-content {
  padding: 0.75rem;
}

.search-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.search-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.15s ease;
  outline: none;
}

.search-item:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.search-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.search-content {
  flex: 1;
  min-width: 0;
}

.search-description {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.remove-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  margin-left: 0.5rem;
  border: none;
  border-radius: 0.25rem;
  background-color: transparent;
  color: #6b7280;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.15s ease;
  outline: none;
}

.remove-button:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

.remove-button:focus {
  outline: 2px solid #dc2626;
  outline-offset: 1px;
}

.show-more-container,
.show-less-container {
  display: flex;
  justify-content: center;
  margin-top: 0.75rem;
}

.show-more-button,
.show-less-button {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.15s ease;
  outline: none;
}

.show-more-button:hover,
.show-less-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.show-more-button:focus,
.show-less-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.empty-state {
  text-align: center;
  padding: 2rem 1rem;
}

.empty-message {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.clear-all-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

.clear-all-button {
  padding: 0.5rem 1rem;
  border: 1px solid #dc2626;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #dc2626;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.15s ease;
  outline: none;
}

.clear-all-button:hover {
  background-color: #dc2626;
  color: #ffffff;
}

.clear-all-button:focus {
  outline: 2px solid #dc2626;
  outline-offset: 2px;
}

/* Confirmation Modal */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirmation-modal {
  background-color: #ffffff;
  border-radius: 0.5rem;
  padding: 1.5rem;
  max-width: 24rem;
  width: 90%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.confirmation-title {
  margin: 0 0 0.75rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.confirmation-message {
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
}

.confirmation-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.confirm-button,
.cancel-button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  outline: none;
}

.confirm-button {
  border: 1px solid #dc2626;
  background-color: #dc2626;
  color: #ffffff;
}

.confirm-button:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.confirm-button:focus {
  outline: 2px solid #dc2626;
  outline-offset: 2px;
}

.cancel-button {
  border: 1px solid #d1d5db;
  background-color: #ffffff;
  color: #374151;
}

.cancel-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.cancel-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Transitions */
.history-expand-enter-active,
.history-expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.history-expand-enter-from,
.history-expand-leave-to {
  opacity: 0;
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.history-expand-enter-to,
.history-expand-leave-from {
  opacity: 1;
  max-height: 500px;
}

/* Responsive Design */
@media (max-width: 640px) {
  .panel-content {
    padding: 0.5rem;
  }
  
  .search-item {
    padding: 0.5rem;
  }
  
  .search-description {
    font-size: 0.8125rem;
  }
  
  .search-time {
    font-size: 0.6875rem;
  }
  
  .confirmation-modal {
    margin: 1rem;
    padding: 1rem;
  }
  
  .confirmation-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .confirm-button,
  .cancel-button {
    width: 100%;
    justify-content: center;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .search-history-panel {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .panel-header {
    background-color: #111827;
    border-color: #374151;
  }
  
  .panel-header:hover {
    background-color: #1f2937;
  }
  
  .panel-title {
    color: #f9fafb;
  }
  
  .expand-icon {
    color: #9ca3af;
  }
  
  .search-item {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .search-item:hover {
    background-color: #111827;
    border-color: #4b5563;
  }
  
  .search-description {
    color: #f9fafb;
  }
  
  .search-time {
    color: #9ca3af;
  }
  
  .confirmation-modal {
    background-color: #1f2937;
  }
  
  .confirmation-title {
    color: #f9fafb;
  }
  
  .confirmation-message {
    color: #d1d5db;
  }
}
</style>