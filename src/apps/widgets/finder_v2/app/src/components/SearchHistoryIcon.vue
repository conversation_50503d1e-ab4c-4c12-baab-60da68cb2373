<template>
  <div v-if="shouldShow" class="search-history-icon-container">
    <!-- History Icon Button -->
    <button
      @click="toggleModal"
      :aria-label="`Search History (${searchCount} searches)`"
      class="history-icon-button"
      :class="{ 'has-history': searchCount > 0 }"
    >
      <!-- Clock/History Icon -->
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="history-icon">
        <path d="M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3Z" />
      </svg>
      
      <!-- Search count badge -->
      <span v-if="searchCount > 0" class="search-count-badge">
        {{ searchCount }}
      </span>
    </button>

    <!-- Modal Overlay -->
    <transition name="modal">
      <div v-if="isModalOpen" class="modal-overlay" @click="closeModal">
        <div class="modal-container" @click.stop>
          <!-- Modal Header -->
          <div class="modal-header">
            <h3 class="modal-title">Search History</h3>
            <button
              @click="closeModal"
              class="close-button"
              aria-label="Close search history"
            >
              ✕
            </button>
          </div>

          <!-- Modal Content -->
          <div class="modal-content">
            <!-- Search History List -->
            <div v-if="allSearches.length > 0" class="search-list">
              <div
                v-for="search in visibleSearches"
                :key="search.id"
                class="search-item"
                @click="executeSearch(search.id)"
                tabindex="0"
                role="button"
                :aria-label="`Execute search for ${search.description}`"
              >
                <div class="search-content">
                  <div class="search-description">{{ search.description }}</div>
                  <div class="search-time">{{ getRelativeTime(search.timestamp) }}</div>
                </div>
                <button
                  class="remove-button"
                  @click.stop="removeSearch(search.id)"
                  :aria-label="`Remove search for ${search.description}`"
                  title="Remove this search"
                >
                  ✕
                </button>
              </div>
            </div>

            <!-- Show More/Less -->
            <div v-if="hasMoreSearches && !showAll" class="show-more-container">
              <button class="show-more-button" @click="showAll = true">
                Show More ({{ remainingCount }} more)
              </button>
            </div>

            <div v-if="showAll && hasMoreSearches" class="show-less-container">
              <button class="show-less-button" @click="showAll = false">
                Show Less
              </button>
            </div>

            <!-- Empty State -->
            <div v-if="allSearches.length === 0" class="empty-state">
              <div class="empty-icon">🔍</div>
              <p class="empty-message">No search history yet</p>
              <p class="empty-hint">Your recent searches will appear here</p>
            </div>

            <!-- Clear All Button -->
            <div v-if="allSearches.length > 0" class="clear-all-container">
              <button class="clear-all-button" @click="confirmClearAll">
                Clear All History
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- Clear Confirmation Modal -->
    <transition name="modal">
      <div v-if="showClearConfirmation" class="modal-overlay" @click="cancelClearAll">
        <div class="confirmation-modal" @click.stop>
          <h4 class="confirmation-title">Clear Search History</h4>
          <p class="confirmation-message">
            Are you sure you want to clear all your search history? This action cannot be undone.
          </p>
          <div class="confirmation-buttons">
            <button class="confirm-button" @click="clearAllHistory">
              Clear All
            </button>
            <button class="cancel-button" @click="cancelClearAll">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { storeToRefs } from 'pinia'
import { useFinderStore } from '../stores/finder'

export default {
  name: 'SearchHistoryIcon',
  setup() {
    const finderStore = useFinderStore()
    const { config } = storeToRefs(finderStore)
    
    // Component state
    const isModalOpen = ref(false)
    const showAll = ref(false)
    const showClearConfirmation = ref(false)
    const searchHistory = ref(null)
    
    // Initialize search history - try immediately and watch for changes
    function updateSearchHistory() {
      const newSearchHistory = finderStore.getSearchHistory()
      console.log('SearchHistoryIcon updateSearchHistory:', newSearchHistory)
      searchHistory.value = newSearchHistory
    }
    
    onMounted(() => {
      updateSearchHistory()
    })
    
    // Watch for config changes to reinitialize search history
    watch(() => config.value, () => {
      updateSearchHistory()
    }, { deep: true })
    
    // Also watch for finder store initialization
    watch(() => finderStore.getSearchHistory(), (newSearchHistory) => {
      console.log('SearchHistoryIcon watch getSearchHistory changed:', newSearchHistory)
      if (newSearchHistory && newSearchHistory !== searchHistory.value) {
        searchHistory.value = newSearchHistory
      }
    })
    
    // Computed properties
    const isEnabled = computed(() => {
      return searchHistory.value?.isEnabled.value ?? false
    })
    
    const allSearches = computed(() => {
      const searches = searchHistory.value?.searches.value ?? []
      console.log('SearchHistoryIcon allSearches debug:', {
        searchHistory: searchHistory.value,
        searches: searches,
        length: searches.length,
        isEnabled: searchHistory.value?.isEnabled.value
      })
      return searches
    })
    
    const displaySearches = computed(() => {
      return searchHistory.value?.displaySearches.value ?? []
    })
    
    const hasMoreSearches = computed(() => {
      return searchHistory.value?.hasMoreSearches.value ?? false
    })
    
    const searchCount = computed(() => {
      return allSearches.value.length
    })
    
    const shouldShow = computed(() => {
      // Show if search history is enabled in config OR if we have a searchHistory instance
      const configEnabled = config.value?.search_history?.enabled !== false
      return configEnabled || isEnabled.value
    })
    
    const visibleSearches = computed(() => {
      if (showAll.value) {
        return allSearches.value
      }
      return displaySearches.value
    })
    
    const remainingCount = computed(() => {
      return Math.max(0, allSearches.value.length - displaySearches.value.length)
    })
    
    // Methods
    function toggleModal() {
      isModalOpen.value = !isModalOpen.value
      if (isModalOpen.value) {
        showAll.value = false
      }
    }
    
    function closeModal() {
      isModalOpen.value = false
      showAll.value = false
    }
    
    async function executeSearch(searchId) {
      try {
        closeModal()
        await finderStore.executeSearchFromHistory(searchId)
        
        // Update search history reference after execution
        searchHistory.value = finderStore.getSearchHistory()
        
        // Scroll to results if available
        setTimeout(() => {
          const resultsElement = document.querySelector('.results-container')
          if (resultsElement) {
            resultsElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
          }
        }, 100)
        
      } catch (error) {
        console.error('Failed to execute search from history:', error)
      }
    }
    
    function removeSearch(searchId) {
      if (searchHistory.value) {
        searchHistory.value.removeSearch(searchId)
        
        // If no searches left, close modal
        if (allSearches.value.length === 0) {
          closeModal()
        }
      }
    }
    
    function confirmClearAll() {
      showClearConfirmation.value = true
    }
    
    function clearAllHistory() {
      if (searchHistory.value) {
        searchHistory.value.clearHistory()
        showClearConfirmation.value = false
        closeModal()
      }
    }
    
    function cancelClearAll() {
      showClearConfirmation.value = false
    }
    
    function getRelativeTime(timestamp) {
      return searchHistory.value?.getRelativeTime(timestamp) || ''
    }
    
    // Keyboard navigation
    function handleKeydown(event) {
      if (event.key === 'Escape') {
        if (showClearConfirmation.value) {
          cancelClearAll()
        } else if (isModalOpen.value) {
          closeModal()
        }
        event.preventDefault()
      }
    }
    
    // Add global keyboard listener
    onMounted(() => {
      document.addEventListener('keydown', handleKeydown)
    })
    
    // Clean up listener
    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleKeydown)
    })
    
    return {
      isModalOpen,
      showAll,
      showClearConfirmation,
      shouldShow,
      allSearches,
      displaySearches,
      hasMoreSearches,
      visibleSearches,
      remainingCount,
      searchCount,
      toggleModal,
      closeModal,
      executeSearch,
      removeSearch,
      confirmClearAll,
      clearAllHistory,
      cancelClearAll,
      getRelativeTime
    }
  }
}
</script>

<style scoped>
.search-history-icon-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}

.history-icon-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  outline: none;
}

.history-icon-button:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.history-icon-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.history-icon-button.has-history {
  background-color: #059669;
}

.history-icon-button.has-history:hover {
  background-color: #047857;
}

.history-icon {
  width: 24px;
  height: 24px;
}

.search-count-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  background-color: #dc2626;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 10px;
  border: 2px solid white;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200;
  padding: 20px;
}

.modal-container {
  background-color: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background-color: transparent;
  color: #6b7280;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.15s ease;
}

.close-button:hover {
  background-color: #e5e7eb;
  color: #374151;
}

.modal-content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.search-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.search-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.15s ease;
  outline: none;
}

.search-item:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.search-content {
  flex: 1;
  min-width: 0;
}

.search-description {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.remove-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin-left: 12px;
  border: none;
  border-radius: 6px;
  background-color: transparent;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.15s ease;
  outline: none;
}

.remove-button:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

.show-more-container,
.show-less-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.show-more-button,
.show-less-button {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #ffffff;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.15s ease;
}

.show-more-button:hover,
.show-less-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.empty-message {
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
}

.empty-hint {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.clear-all-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.clear-all-button {
  padding: 10px 20px;
  border: 1px solid #dc2626;
  border-radius: 6px;
  background-color: #ffffff;
  color: #dc2626;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.15s ease;
}

.clear-all-button:hover {
  background-color: #dc2626;
  color: #ffffff;
}

/* Confirmation Modal */
.confirmation-modal {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.confirmation-title {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.confirmation-message {
  margin: 0 0 24px 0;
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
}

.confirmation-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirm-button,
.cancel-button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.confirm-button {
  border: 1px solid #dc2626;
  background-color: #dc2626;
  color: #ffffff;
}

.confirm-button:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.cancel-button {
  border: 1px solid #d1d5db;
  background-color: #ffffff;
  color: #374151;
}

.cancel-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 640px) {
  .search-history-icon-container {
    bottom: 16px;
    right: 16px;
  }
  
  .history-icon-button {
    width: 44px;
    height: 44px;
  }
  
  .history-icon {
    width: 20px;
    height: 20px;
  }
  
  .modal-overlay {
    padding: 16px;
  }
  
  .modal-container {
    max-width: none;
  }
  
  .modal-header,
  .modal-content {
    padding: 20px;
  }
  
  .search-item {
    padding: 12px;
  }
  
  .confirmation-modal {
    padding: 20px;
  }
  
  .confirmation-buttons {
    flex-direction: column;
  }
  
  .confirm-button,
  .cancel-button {
    width: 100%;
    justify-content: center;
  }
}
</style>