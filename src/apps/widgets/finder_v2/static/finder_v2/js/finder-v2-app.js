var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,o=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,n=(e,a,l)=>new Promise((t,o)=>{var n=e=>{try{i(l.next(e))}catch(a){o(a)}},s=e=>{try{i(l.throw(e))}catch(a){o(a)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(n,s);i((l=l.apply(e,a)).next())});import{r as s,c as i,a as r,b as d,d as c,e as u,f as m,o as v,F as g,A as p,j as f,I as h,w as y,g as w,h as b,i as M,k,l as x,m as C,n as S,p as L,q as V,t as _,T,s as A,u as I,v as D,x as F,y as $,z as O,B as H,C as Y,D as E}from"./listbox-B6xKsyeJ.js";function G(e){const a=s([]),l=s(!0),t=s(10),o=s(5),n=`finder_v2_search_history_${e}`,d=i(()=>{try{const e="__localStorage_test__";return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return console.warn("localStorage not available:",e),!1}});function c(){if(a.value=[],d.value)try{localStorage.removeItem(n)}catch(e){console.warn("Failed to clear search history:",e)}}function u(e){l.value=e,e||c(),f()}const m=i(()=>a.value.slice(0,o.value)),v=i(()=>a.value.length>o.value);function g(e){const a=[];return e.year&&a.push(e.year),e.make&&a.push(e.make),e.model&&a.push(e.model),e.modification&&a.push(e.modification),e.generation&&"alternative"===e.flowType&&a.push(`(${e.generation})`),a.join(" ")||"Unknown Search"}function p(e){return!(!e||"object"!=typeof e)&&(!!(e.id&&e.timestamp&&e.parameters)&&(!("number"!=typeof e.timestamp||e.timestamp<=0)&&!(!e.parameters||"object"!=typeof e.parameters)))}function f(){if(d.value)try{const e={version:"1.0",enabled:l.value,searches:a.value};localStorage.setItem(n,JSON.stringify(e))}catch(e){console.warn("Failed to save search history:",e),"QuotaExceededError"===e.name&&function(){try{const e=a.value.slice(0,Math.floor(a.value.length/2));a.value=e,f()}catch(e){console.warn("Failed to handle quota exceeded:",e),c()}}()}}return function(){if(d.value)try{const e=localStorage.getItem(n);if(e){const t=JSON.parse(e);!function(e){if(!e||"object"!=typeof e)return!1;if(!Array.isArray(e.searches))return!1;return!0}(t)?c():(a.value=t.searches||[],l.value=!1!==t.enabled,a.value=a.value.filter(p),a.value.sort((e,a)=>a.timestamp-e.timestamp))}}catch(e){console.warn("Failed to load search history:",e),c()}else l.value=!1}(),{searches:r(a),isEnabled:r(l),displaySearches:m,hasMoreSearches:v,maxItems:r(t),displayItems:r(o),isLocalStorageAvailable:d,addSearch:function(e){if(l.value&&d.value)try{const l=function(e){const a=Date.now();return{id:`${a}_${Math.random().toString(36).substr(2,9)}`,timestamp:a,description:g(e),flowType:e.flowType||"primary",parameters:{year:e.year||"",make:e.make||"",model:e.model||"",modification:e.modification||"",generation:e.generation||""}}}(e),o=a.value.findIndex(e=>{return a=e.parameters,t=l.parameters,["year","make","model","modification","generation"].every(e=>(a[e]||"")===(t[e]||""));var a,t});-1!==o&&a.value.splice(o,1),a.value.unshift(l),a.value.length>t.value&&(a.value=a.value.slice(0,t.value)),f()}catch(o){console.warn("Failed to add search to history:",o)}},removeSearch:function(e){if(l.value)try{const l=a.value.findIndex(a=>a.id===e);-1!==l&&(a.value.splice(l,1),f())}catch(t){console.warn("Failed to remove search from history:",t)}},getSearch:function(e){return a.value.find(a=>a.id===e)||null},updateSearchTimestamp:function(e){if(l.value)try{const l=a.value.findIndex(a=>a.id===e);if(-1!==l){const e=a.value.splice(l,1)[0];e.timestamp=Date.now(),a.value.unshift(e),f()}}catch(t){console.warn("Failed to update search timestamp:",t)}},clearHistory:c,setEnabled:u,configure:function(e){void 0!==e.maxItems&&(t.value=Math.max(1,Math.min(50,e.maxItems))),void 0!==e.displayItems&&(o.value=Math.max(1,Math.min(t.value,e.displayItems))),void 0!==e.enabled&&u(e.enabled),a.value.length>t.value&&(a.value=a.value.slice(0,t.value),f())},getRelativeTime:function(e){const a=Date.now()-e,l=Math.floor(a/1e3),t=Math.floor(l/60),o=Math.floor(t/60),n=Math.floor(o/24);return n>0?`${n} day${n>1?"s":""} ago`:o>0?`${o} hour${o>1?"s":""} ago`:t>0?`${t} minute${t>1?"s":""} ago`:"Just now"}}}d.defaults.paramsSerializer=e=>{const a=new URLSearchParams;return Object.entries(e).forEach(([e,l])=>{Array.isArray(l)?l.forEach(l=>{null!=l&&""!==l&&a.append(e,l)}):null!=l&&""!==l&&a.append(e,l)}),a.toString()};const z=c("finder",()=>{const e=s({}),a=s(!1),l=s(null),t=s([]),o=s("");let r=null;const c=s(!1),u=s(!1),m=s(!1),v=s(!1),g=s(!1),p=s(!1),f=s(!1),h=s(!1),y=s(!1),w=s(!1),b=s(new Map),M=s(""),k=s(""),x=s(""),C=s(""),S=s(""),L=s([]),V=s([]),_=s([]),T=s([]),A=s([]),I=i(()=>e.value.flowType||"primary"),D=i(()=>e.value.apiVersion||"v2"),F=i(()=>e.value.widgetResources||{});function $(e=null,a=null){return n(this,null,function*(){var t;try{c.value=!0,p.value=!1;const l={};e&&(l.make=e),a&&(l.model=a);const o=yield P("year",l);L.value=(null==(t=o.data)?void 0:t.data)||o.data||[],p.value=!0}catch(o){l.value=o.message}finally{c.value=!1}})}function O(e=null){return n(this,null,function*(){var a;try{u.value=!0,f.value=!1;const l=e?{year:e}:{};Object.assign(l,B());const t=yield P("make",l);V.value=(null==(a=t.data)?void 0:a.data)||t.data||[],f.value=!0}catch(t){l.value=t.message}finally{u.value=!1}})}function H(e,a=null){return n(this,null,function*(){var t;try{m.value=!0,h.value=!1;const l={make:e};a&&(l.year=a);const o=yield P("model",l);_.value=(null==(t=o.data)?void 0:t.data)||o.data||[],h.value=!0}catch(o){l.value=o.message}finally{m.value=!1}})}function Y(e,a,t=null){return n(this,null,function*(){var o;try{g.value=!0,w.value=!1;const l={make:e,model:a};t&&("primary"===I.value||"year_select"===I.value?l.year=t:"alternative"===I.value&&(l.generation=t));const n=yield P("modification",l);T.value=(null==(o=n.data)?void 0:o.data)||n.data||[],w.value=!0}catch(n){l.value=n.message}finally{g.value=!1}})}function E(e,a){return n(this,null,function*(){var t;try{v.value=!0,y.value=!1;const l={make:e,model:a},o=yield P("generation",l);A.value=(null==(t=o.data)?void 0:t.data)||o.data||[],y.value=!0}catch(o){l.value=o.message}finally{v.value=!1}})}function z(){return n(this,null,function*(){var e;try{a.value=!0,l.value=null;const o={make:k.value,model:x.value};"primary"===I.value||"year_select"===I.value?(o.year=M.value,o.modification=C.value):"alternative"===I.value&&(o.generation=S.value,o.modification=C.value);const n=yield P("search_by_model",o);if(t.value=(null==(e=n.data)?void 0:e.data)||n.data||[],r&&t.value.length>0){const e={flowType:I.value,year:M.value,make:k.value,model:x.value,modification:C.value,generation:S.value};r.addSearch(e)}setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},100)}catch(o){l.value=o.message}finally{a.value=!1}})}function R(){M.value="",k.value="",x.value="",C.value="",S.value="",_.value=[],T.value=[],A.value=[]}function j(){t.value=[],setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},50)}function B(){var a,l,t,o,n;const s=(null==(l=null==(a=e.value)?void 0:a.content)?void 0:l.filter)||(null==(t=e.value)?void 0:t.filter)||{},i=s.by||(null==(n=null==(o=e.value)?void 0:o.content)?void 0:n.by)||"",r=e=>"string"==typeof e?e:(null==e?void 0:e.slug)||(null==e?void 0:e.value)||"",d={};if("brands"===i&&Array.isArray(s.brands)&&s.brands.length){const e=s.brands.map(r).filter(Boolean);e.length&&(d.brands=e.join(","))}else if("brands_exclude"===i&&Array.isArray(s.brands_exclude)&&s.brands_exclude.length){const e=s.brands_exclude.map(r).filter(Boolean);e.length&&(d.brands_exclude=e.join(","))}return d}function U(){var a,l;const t=(null==(l=null==(a=e.value)?void 0:a.content)?void 0:l.regions)||[];return t.length?{region:t}:{}}function P(a){return n(this,arguments,function*(a,l={}){const t=function(e,a){const l=Object.keys(a).sort().reduce((e,l)=>(e[l]=a[l],e),{});return`${e}:${JSON.stringify(l)}`}(a,l);if(b.value.has(t))return yield b.value.get(t);const o=function(a){return n(this,arguments,function*(a,l={}){const t=F.value[a];if(!t||!t[1])throw new Error(`API endpoint not configured: ${a}`);let o=t[1];return o.startsWith("/")&&e.value.baseUrl&&(o=e.value.baseUrl+o),["make","model","year","generation","modification"].includes(a)&&Object.assign(l,U()),yield d.get(o,{params:l})})}(a,l);b.value.set(t,o);try{return yield o}finally{b.value.delete(t)}})}return{config:e,loading:a,error:l,results:t,outputTemplate:o,loadingYears:c,loadingMakes:u,loadingModels:m,loadingGenerations:v,loadingModifications:g,stateLoadedYears:p,stateLoadedMakes:f,stateLoadedModels:h,stateLoadedGenerations:y,stateLoadedModifications:w,selectedYear:M,selectedMake:k,selectedModel:x,selectedModification:C,selectedGeneration:S,years:L,makes:V,models:_,modifications:T,generations:A,flowType:I,apiVersion:D,widgetResources:F,initialize:function(t){var s;e.value=t,o.value=(null==(s=t.interface)?void 0:s.outputTemplate)||"",console.log("Widget config debug:",t);const i=t.id||t.uuid||t.widgetUuid||"default";console.log("Using widget ID for search history:",i),r=G(i);const d=t.search_history||{};Object.keys(d).length>0&&r.configure(d),function(){n(this,null,function*(){try{a.value=!0,l.value=null,"primary"===I.value?yield $():yield O()}catch(e){l.value=e.message}finally{a.value=!1}})}()},loadYears:$,loadMakes:O,loadModels:H,loadModifications:Y,loadGenerations:E,searchByVehicle:z,resetVehicleSearch:R,clearResults:j,buildBrandFilterParams:B,buildRegionParams:U,executeSearchFromHistory:function(e){return n(this,null,function*(){if(!r)return void console.warn("Search history not initialized");const a=r.getSearch(e);if(a)try{j(),yield function(e){return n(this,null,function*(){const a=e.parameters,l=e.flowType||"primary";R();try{"primary"===l?(a.year&&(M.value=a.year,yield O(a.year)),a.make&&(k.value=a.make,yield H(a.make,a.year)),a.model&&(x.value=a.model,yield Y(a.make,a.model,a.year)),a.modification&&(C.value=a.modification)):"alternative"===l?(a.make&&(k.value=a.make,yield H(a.make)),a.model&&(x.value=a.model,yield E(a.make,a.model)),a.generation&&(S.value=a.generation,yield Y(a.make,a.model,a.generation)),a.modification&&(C.value=a.modification)):"year_select"===l&&(a.make&&(k.value=a.make,yield H(a.make)),a.model&&(x.value=a.model,yield $(a.make,a.model)),a.year&&(M.value=a.year,yield Y(a.make,a.model,a.year)),a.modification&&(C.value=a.modification))}catch(t){throw console.error("Failed to populate form from search:",t),t}})}(a),r.updateSearchTimestamp(e),yield z()}catch(l){console.error("Failed to execute search from history:",l),l.value="Failed to execute search from history"}else console.warn("Search not found:",e)})},getSearchHistory:function(){return r}}});const R=(e,a)=>{const l=e.__vccOpts||e;for(const[t,o]of a)l[t]=o;return l},j={name:"CustomSelector",components:{Listbox:h,ListboxButton:f,ListboxOptions:p,ListboxOption:g,CheckIcon:function(e,a){return v(),u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z","clip-rule":"evenodd"})])},ChevronUpDownIcon:function(e,a){return v(),u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"fill-rule":"evenodd",d:"M10.53 3.47a.75.75 0 0 0-1.06 0L6.22 6.72a.75.75 0 0 0 1.06 1.06L10 5.06l2.72 2.72a.75.75 0 1 0 1.06-1.06l-3.25-3.25Zm-4.31 9.81 3.25 3.25a.75.75 0 0 0 1.06 0l3.25-3.25a.75.75 0 1 0-1.06-1.06L10 14.94l-2.72-2.72a.75.75 0 0 0-1.06 1.06Z","clip-rule":"evenodd"})])}},props:{modelValue:{type:[String,Number],default:""},options:{type:Array,default:()=>[]},placeholder:{type:String,default:"Select option"},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},preloader:{type:Boolean,default:!1},stateLoaded:{type:Boolean,default:!1},autoExpand:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(e,{emit:a}){const l=s(null),t=i({get:()=>e.modelValue,set:e=>{a("update:modelValue",e),a("change",e)}}),o=i(()=>e.modelValue?e.options.find(a=>(a.slug||a.id)===e.modelValue):null);y(()=>e.options,a=>{if(e.modelValue&&a.length>0){a.some(a=>(a.slug||a.id)===e.modelValue)||(t.value="")}});const r=window.matchMedia("(prefers-reduced-motion: reduce)").matches;y(()=>[e.stateLoaded,e.options.length],([a,t],[o])=>{var s,i;if(!o&&a&&t>0&&!e.disabled&&e.autoExpand&&!r){const a=document.activeElement,o=null!=(i=null==(s=l.value)?void 0:s.$el)?i:l.value;if(a&&a!==document.body&&o&&!o.contains(a)&&["INPUT","TEXTAREA"].includes(a.tagName))return;console.debug("AutoExpand →",{placeholder:e.placeholder,len:t}),setTimeout(()=>n(this,null,function*(){var a,t;yield S();const o=null!=(t=null==(a=l.value)?void 0:a.$el)?t:l.value;try{o&&"function"==typeof o.click&&(o.focus({preventScroll:!0}),o.click(),console.debug("AutoExpand click dispatched",{placeholder:e.placeholder}),d())}catch(n){console.warn("Auto-expand failed:",n)}}),200)}},{flush:"post"});const d=()=>{window.parentIFrame&&window.parentIFrame.size&&(window.parentIFrame.size(),setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},350))};return y(()=>e.modelValue,()=>{d()}),w(()=>{var e;l.value&&(null!=(e=l.value.$el)?e:l.value).addEventListener("click",d)}),b(()=>{var e;l.value&&(null!=(e=l.value.$el)?e:l.value).removeEventListener("click",d)}),{selectedValue:t,selectedOption:o,getDisplayText:e=>{if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const a=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?`${e.name}, ${a}`:a}return e.name||""},getDisplayData:e=>{var a,l,t,o,n,s;if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const a=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?{isGeneration:!0,isModification:!1,name:e.name,yearRanges:a}:{isGeneration:!1,isModification:!1,name:a,yearRanges:""}}if(e.engine||e.trim||e.generation){const i=[];let r=e.name||"";if(("primary"===(null==(a=window.FinderV2Config)?void 0:a.flowType)||"primary"===(null==(t=null==(l=window.FinderV2Config)?void 0:l.widgetConfig)?void 0:t.flowType)||"year_select"===(null==(o=window.FinderV2Config)?void 0:o.flowType)||"year_select"===(null==(s=null==(n=window.FinderV2Config)?void 0:n.widgetConfig)?void 0:s.flowType))&&e.generation){const a=e.generation;let l="";if(a.name&&""!==a.name.trim()){const e=`${a.start}-${a.end}`;l=`${a.name} (${e})`}else l=`${a.start}-${a.end}`;l&&i.push(l)}if(e.trim&&""!==e.trim.trim()&&(e.trim,e.name),e.engine){const a=[];if(e.engine.capacity&&a.push(e.engine.capacity),e.engine.type&&a.push(e.engine.type),e.engine.fuel){let l=e.engine.fuel;"Petrol"===e.engine.fuel?l="Petrol":"Diesel"===e.engine.fuel?l="Diesel":"Hybrid"===e.engine.fuel&&(l="Hybrid"),a.push(l)}e.engine.power&&e.engine.power.hp&&a.push(`${e.engine.power.hp}HP`),a.length>0&&i.push(a.join(" "))}return{isGeneration:!1,isModification:!0,name:r,details:i.join(" • ")}}return{isGeneration:!1,isModification:!1,name:e.name||"",yearRanges:""}},buttonRef:l,triggerResize:d}}},B={class:"flex items-start"},U={class:"relative"},P={key:0,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},N={key:1,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},W={class:"truncate"},Z={class:"truncate text-ws-secondary-500"},J={key:1,class:"flex flex-col min-w-0"},q={class:"truncate font-medium"},X={key:0,class:"truncate text-xs text-ws-secondary-500"},K={key:2,class:"truncate"},Q={key:2,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},ee={class:"flex"},ae={key:1,class:"flex flex-col min-w-0 w-full"},le={class:"min-w-[32px] mt-2 ml-1"},te={key:0,class:"spinner-external"};const oe={key:0,class:"search-history-icon-container"},ne=["aria-label"],se={key:0,class:"search-count-badge"},ie={class:"modal-header"},re={class:"modal-content"},de={key:0,class:"search-list"},ce=["onClick","aria-label"],ue={class:"search-content"},me={class:"search-description"},ve={class:"search-time"},ge=["onClick","aria-label"],pe={key:1,class:"show-more-container"},fe={key:2,class:"show-less-container"},he={key:3,class:"empty-state"},ye={key:4,class:"clear-all-container"},we={class:"confirmation-buttons"};const be={class:"vehicle-search"},Me={key:0,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},ke={class:"form-group"},xe={class:"form-group"},Ce={class:"form-group"},Se={class:"form-group"},Le={key:1,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ve={class:"form-group"},_e={class:"form-group"},Te={class:"form-group"},Ae={class:"form-group"},Ie={key:2,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},De={class:"form-group"},Fe={class:"form-group"},$e={class:"form-group"},Oe={class:"form-group"},He={key:0,class:"error-container"},Ye={class:"error"};const Ee=e=>{const a=document.createElement("div");return a.textContent=e,a.innerHTML},Ge=(e,a)=>a.split(".").reduce((e,a)=>e&&void 0!==e[a]?e[a]:"",e),ze=(e,n)=>e.replace(/\{\%\s*for\s+(\w+)\s+in\s+(\w+)\s*\%\}([\s\S]*?)\{\%\s*endfor\s*\%\}/g,(e,s,i,r)=>{const d=n[i];return Array.isArray(d)?d.map(e=>{let i=r;const d=((e,n)=>{for(var s in n||(n={}))l.call(n,s)&&o(e,s,n[s]);if(a)for(var s of a(n))t.call(n,s)&&o(e,s,n[s]);return e})({[s]:e},n);return i=Re(i,d),i=je(i,d),i}).join(""):""}),Re=(e,a)=>{let l=e,t=!0;for(;t;){t=!1;const e=/\{\%\s*if\s+(.*?)\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?)(?:\{\%\s*else\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?))?\{\%\s*endif\s*\%\}/g;l=l.replace(e,(e,l,o,n="")=>{t=!0;let s=Be(l,a)?o:n;return s.includes("{% if")&&(s=Re(s,a)),s})}return l},je=(e,a)=>e.replace(/\{\{\s*(.*?)\s*\}\}/g,(e,l)=>{const t=l.trim(),o=t.match(/^(.+?)\s*\?\s*['"]?(.*?)['"]?\s*:\s*['"]?(.*?)['"]?$/);if(o){const[,e,l,t]=o,n=Be(e.trim(),a),s=l.replace(/^['"]|['"]$/g,""),i=t.replace(/^['"]|['"]$/g,"");return Ee(n?s:i)}const n=Ge(a,t);return Ee(String(n||""))}),Be=(e,a)=>{try{if(e.startsWith("not ")){const l=e.slice(4).trim();return!Be(l,a)}const l=Ge(a,e.trim());return Boolean(l)}catch(l){return!1}},Ue={class:"results-display"},Pe={key:0,class:"loading"},Ne={key:1,class:"no-results"},We={key:2,class:"results-content"},Ze={key:0,class:"custom-results"},Je=["innerHTML"],qe={key:1,class:"default-results"},Xe={class:"text-lg font-semibold mb-2"},Ke={class:"font-medium uppercase tracking-wide text-sm mb-1"},Qe={class:"grid grid-cols-2 gap-2 text-sm"},ea={key:0},aa={key:2,class:"wheel-size-button-container"},la=["href"];const ta={class:"search-content"},oa={key:0,class:"results-section"};const na=Y(R({name:"FinderV2Widget",components:{VehicleSearch:R({name:"VehicleSearch",components:{CustomSelector:R(j,[["render",function(e,a,l,t,o,n){const s=C("ListboxButton"),i=C("ListboxOption"),r=C("CheckIcon"),d=C("ListboxOptions"),c=C("Listbox");return v(),u("div",B,[M(c,{modelValue:t.selectedValue,"onUpdate:modelValue":a[0]||(a[0]=e=>t.selectedValue=e),disabled:l.disabled,class:"flex-1"},{default:k(()=>[m("div",U,[M(s,{as:"button",class:L(["grid w-full cursor-default grid-cols-1 theme-rounded-md bg-white py-1.5 pr-2 pl-3 text-left text-gray-900 border border-gray-300 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 focus:border-indigo-600 sm:text-sm/6",l.disabled?"bg-gray-100 text-gray-500 cursor-not-allowed":""]),ref:"buttonRef"},{default:k(()=>[l.loading?(v(),u("span",P,a[1]||(a[1]=[m("svg",{class:"animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[m("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),m("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),m("span",{class:"truncate"},"Loading...",-1)]))):t.selectedOption?(v(),u("span",N,[t.getDisplayData(t.selectedOption).isGeneration?(v(),u(V,{key:0},[m("span",W,_(t.getDisplayData(t.selectedOption).name),1),m("span",Z,_(t.getDisplayData(t.selectedOption).yearRanges),1)],64)):t.getDisplayData(t.selectedOption).isModification?(v(),u("div",J,[m("span",q,_(t.getDisplayData(t.selectedOption).name),1),t.getDisplayData(t.selectedOption).details?(v(),u("span",X,_(t.getDisplayData(t.selectedOption).details),1)):x("",!0)])):(v(),u("span",K,_(t.getDisplayText(t.selectedOption)),1))])):(v(),u("span",Q,[m("span",{class:L(["truncate",l.disabled?"text-gray-500":"text-gray-900"])},_(l.placeholder),3)])),a[2]||(a[2]=m("svg",{class:"col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 sm:size-4",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true"},[m("path",{"fill-rule":"evenodd",d:"M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z","clip-rule":"evenodd"})],-1))]),_:1,__:[2]},8,["class"]),M(T,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:k(()=>[M(d,{class:"z-10 mt-1 max-h-60 w-full overflow-auto theme-rounded-md bg-white py-1 text-base ring-1 shadow-lg ring-black/5 focus:outline-hidden sm:text-sm"},{default:k(()=>[l.options.length||l.loading?x("",!0):(v(),A(i,{key:0,value:null,class:"relative cursor-default select-none py-2 pr-9 pl-3 text-gray-500",disabled:""},{default:k(()=>a[3]||(a[3]=[I(" No options available ")])),_:1,__:[3]})),(v(!0),u(V,null,D(l.options,e=>(v(),A(i,{key:e.slug||e.id,value:e.slug||e.id,as:"template"},{default:k(({active:a,selected:l})=>[m("li",{class:L([a?"bg-indigo-600 text-white outline-hidden":"text-gray-900","relative cursor-default select-none py-2 pr-9 pl-3"])},[m("div",ee,[t.getDisplayData(e).isGeneration?(v(),u(V,{key:0},[m("span",{class:L([l?"font-semibold":"font-normal","truncate"])},_(t.getDisplayData(e).name),3),m("span",{class:L([a?"text-indigo-200":"text-ws-secondary-500","ml-2 truncate"])},_(t.getDisplayData(e).yearRanges),3)],64)):t.getDisplayData(e).isModification?(v(),u("div",ae,[m("span",{class:L([l?"font-semibold":"font-medium","truncate"])},_(t.getDisplayData(e).name),3),t.getDisplayData(e).details?(v(),u("span",{key:0,class:L([a?"text-indigo-200":"text-ws-secondary-500","truncate text-xs"])},_(t.getDisplayData(e).details),3)):x("",!0)])):(v(),u("span",{key:2,class:L([l?"font-semibold":"font-normal","truncate"])},_(t.getDisplayText(e)),3))]),l?(v(),u("span",{key:0,class:L([a?"text-white":"text-indigo-600","absolute inset-y-0 right-0 flex items-center pr-4"])},[M(r,{class:"size-5","aria-hidden":"true"})],2)):x("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue","disabled"]),m("div",le,[l.preloader?(v(),u("div",te,a[4]||(a[4]=[m("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"w-5 h-5 text-gray-400"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)]))):x("",!0)])])}],["__scopeId","data-v-fc79c1a7"]]),SearchHistoryIcon:R({name:"SearchHistoryIcon",setup(){const e=z(),{config:a}=F(e),l=s(!1),t=s(!1),o=s(!1),r=s(null);function d(){const a=e.getSearchHistory();console.log("SearchHistoryIcon updateSearchHistory:",a),r.value=a}w(()=>{d()}),y(()=>a.value,()=>{d()},{deep:!0}),y(()=>e.getSearchHistory(),e=>{console.log("SearchHistoryIcon watch getSearchHistory changed:",e),e&&e!==r.value&&(r.value=e)});const c=i(()=>{var e,a;return null!=(a=null==(e=r.value)?void 0:e.isEnabled.value)&&a}),u=i(()=>{var e,a,l;const t=null!=(a=null==(e=r.value)?void 0:e.searches.value)?a:[];return console.log("SearchHistoryIcon allSearches debug:",{searchHistory:r.value,searches:t,length:t.length,isEnabled:null==(l=r.value)?void 0:l.isEnabled.value}),t}),m=i(()=>{var e,a;return null!=(a=null==(e=r.value)?void 0:e.displaySearches.value)?a:[]}),v=i(()=>{var e,a;return null!=(a=null==(e=r.value)?void 0:e.hasMoreSearches.value)&&a}),g=i(()=>u.value.length),p=i(()=>{var e,l;return!1!==(null==(l=null==(e=a.value)?void 0:e.search_history)?void 0:l.enabled)||c.value}),f=i(()=>t.value?u.value:m.value),h=i(()=>Math.max(0,u.value.length-m.value.length));function b(){l.value=!1,t.value=!1}function M(){o.value=!1}function k(e){"Escape"===e.key&&(o.value?M():l.value&&b(),e.preventDefault())}return w(()=>{document.addEventListener("keydown",k)}),$(()=>{document.removeEventListener("keydown",k)}),{isModalOpen:l,showAll:t,showClearConfirmation:o,shouldShow:p,allSearches:u,displaySearches:m,hasMoreSearches:v,visibleSearches:f,remainingCount:h,searchCount:g,toggleModal:function(){l.value=!l.value,l.value&&(t.value=!1)},closeModal:b,executeSearch:function(a){return n(this,null,function*(){try{b(),yield e.executeSearchFromHistory(a),r.value=e.getSearchHistory(),setTimeout(()=>{const e=document.querySelector(".results-container");e&&e.scrollIntoView({behavior:"smooth",block:"start"})},100)}catch(l){console.error("Failed to execute search from history:",l)}})},removeSearch:function(e){r.value&&(r.value.removeSearch(e),0===u.value.length&&b())},confirmClearAll:function(){o.value=!0},clearAllHistory:function(){r.value&&(r.value.clearHistory(),o.value=!1,b())},cancelClearAll:M,getRelativeTime:function(e){var a;return(null==(a=r.value)?void 0:a.getRelativeTime(e))||""}}}},[["render",function(e,a,l,t,o,n){return t.shouldShow?(v(),u("div",oe,[m("button",{onClick:a[0]||(a[0]=(...e)=>t.toggleModal&&t.toggleModal(...e)),"aria-label":`Search History (${t.searchCount} searches)`,class:L(["history-icon-button",{"has-history":t.searchCount>0}])},[a[11]||(a[11]=m("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",class:"history-icon"},[m("path",{d:"M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3Z"})],-1)),t.searchCount>0?(v(),u("span",se,_(t.searchCount),1)):x("",!0)],10,ne),M(T,{name:"modal"},{default:k(()=>[t.isModalOpen?(v(),u("div",{key:0,class:"modal-overlay",onClick:a[6]||(a[6]=(...e)=>t.closeModal&&t.closeModal(...e))},[m("div",{class:"modal-container",onClick:a[5]||(a[5]=O(()=>{},["stop"]))},[m("div",ie,[a[12]||(a[12]=m("h3",{class:"modal-title"},"Search History",-1)),m("button",{onClick:a[1]||(a[1]=(...e)=>t.closeModal&&t.closeModal(...e)),class:"close-button","aria-label":"Close search history"}," ✕ ")]),m("div",re,[t.allSearches.length>0?(v(),u("div",de,[(v(!0),u(V,null,D(t.visibleSearches,e=>(v(),u("div",{key:e.id,class:"search-item",onClick:a=>t.executeSearch(e.id),tabindex:"0",role:"button","aria-label":`Execute search for ${e.description}`},[m("div",ue,[m("div",me,_(e.description),1),m("div",ve,_(t.getRelativeTime(e.timestamp)),1)]),m("button",{class:"remove-button",onClick:O(a=>t.removeSearch(e.id),["stop"]),"aria-label":`Remove search for ${e.description}`,title:"Remove this search"}," ✕ ",8,ge)],8,ce))),128))])):x("",!0),t.hasMoreSearches&&!t.showAll?(v(),u("div",pe,[m("button",{class:"show-more-button",onClick:a[2]||(a[2]=e=>t.showAll=!0)}," Show More ("+_(t.remainingCount)+" more) ",1)])):x("",!0),t.showAll&&t.hasMoreSearches?(v(),u("div",fe,[m("button",{class:"show-less-button",onClick:a[3]||(a[3]=e=>t.showAll=!1)}," Show Less ")])):x("",!0),0===t.allSearches.length?(v(),u("div",he,a[13]||(a[13]=[m("div",{class:"empty-icon"},"🔍",-1),m("p",{class:"empty-message"},"No search history yet",-1),m("p",{class:"empty-hint"},"Your recent searches will appear here",-1)]))):x("",!0),t.allSearches.length>0?(v(),u("div",ye,[m("button",{class:"clear-all-button",onClick:a[4]||(a[4]=(...e)=>t.confirmClearAll&&t.confirmClearAll(...e))}," Clear All History ")])):x("",!0)])])])):x("",!0)]),_:1}),M(T,{name:"modal"},{default:k(()=>[t.showClearConfirmation?(v(),u("div",{key:0,class:"modal-overlay",onClick:a[10]||(a[10]=(...e)=>t.cancelClearAll&&t.cancelClearAll(...e))},[m("div",{class:"confirmation-modal",onClick:a[9]||(a[9]=O(()=>{},["stop"]))},[a[14]||(a[14]=m("h4",{class:"confirmation-title"},"Clear Search History",-1)),a[15]||(a[15]=m("p",{class:"confirmation-message"}," Are you sure you want to clear all your search history? This action cannot be undone. ",-1)),m("div",we,[m("button",{class:"confirm-button",onClick:a[7]||(a[7]=(...e)=>t.clearAllHistory&&t.clearAllHistory(...e))}," Clear All "),m("button",{class:"cancel-button",onClick:a[8]||(a[8]=(...e)=>t.cancelClearAll&&t.cancelClearAll(...e))}," Cancel ")])])])):x("",!0)]),_:1})])):x("",!0)}],["__scopeId","data-v-13ee27c6"]])},setup(){const e=z(),{loading:a,error:l,selectedYear:t,selectedMake:o,selectedModel:r,selectedModification:d,selectedGeneration:c,years:u,makes:m,models:v,modifications:g,generations:p,flowType:f,loadingYears:h,loadingMakes:w,loadingModels:b,loadingGenerations:M,loadingModifications:k,stateLoadedYears:x,stateLoadedMakes:C,stateLoadedModels:S,stateLoadedGenerations:L,stateLoadedModifications:V}=F(e),_=i(()=>"primary"===f.value?t.value&&o.value&&r.value&&d.value:"alternative"===f.value?o.value&&r.value&&c.value&&d.value:"year_select"===f.value&&(o.value&&r.value&&t.value&&d.value)),T=s(!1);function A(){return n(this,null,function*(){_.value&&(yield e.searchByVehicle())})}return y(_,(e,a)=>n(this,null,function*(){e&&!a&&(T.value=!0,yield A(),setTimeout(()=>{T.value=!1},100))})),y(d,(e,l)=>n(this,null,function*(){!e||e===l||a.value||T.value||(yield A())})),{loading:a,error:l,selectedYear:t,selectedMake:o,selectedModel:r,selectedModification:d,selectedGeneration:c,years:u,makes:m,models:v,modifications:g,generations:p,flowType:f,loadingYears:h,loadingMakes:w,loadingModels:b,loadingGenerations:M,loadingModifications:k,stateLoadedYears:x,stateLoadedMakes:C,stateLoadedModels:S,stateLoadedGenerations:L,stateLoadedModifications:V,canSearch:_,onYearChange:function(){return n(this,null,function*(){"primary"===f.value?(e.selectedMake="",e.selectedModel="",e.selectedModification="",e.models=[],e.modifications=[],e.stateLoadedMakes=!1,e.stateLoadedModels=!1,e.stateLoadedModifications=!1,t.value&&(yield e.loadMakes(t.value))):"year_select"===f.value&&(e.selectedModification="",e.modifications=[],e.stateLoadedModifications=!1,t.value&&(yield e.loadModifications(o.value,r.value,t.value)))})},onMakeChange:function(){return n(this,null,function*(){e.selectedModel="",e.selectedModification="",e.selectedGeneration="",e.models=[],e.modifications=[],e.generations=[],e.stateLoadedModels=!1,e.stateLoadedModifications=!1,e.stateLoadedGenerations=!1,o.value&&("primary"===f.value?yield e.loadModels(o.value,t.value):"alternative"!==f.value&&"year_select"!==f.value||(yield e.loadModels(o.value)))})},onModelChange:function(){return n(this,null,function*(){e.selectedModification="",e.selectedGeneration="",e.modifications=[],e.generations=[],e.stateLoadedModifications=!1,e.stateLoadedGenerations=!1,"year_select"===f.value&&(e.selectedYear="",e.years=[],e.stateLoadedYears=!1),r.value&&("primary"===f.value?yield e.loadModifications(o.value,r.value,t.value):"alternative"===f.value?yield e.loadGenerations(o.value,r.value):"year_select"===f.value&&(yield e.loadYears(o.value,r.value)))})},onGenerationChange:function(){return n(this,null,function*(){e.selectedModification="",e.modifications=[],e.stateLoadedModifications=!1,c.value&&(yield e.loadModifications(o.value,r.value,c.value))})},handleSearch:A}}},[["render",function(e,a,l,t,o,n){const s=C("SearchHistoryIcon"),i=C("CustomSelector");return v(),u("div",be,[M(s),m("form",{onSubmit:a[12]||(a[12]=O((...e)=>t.handleSearch&&t.handleSearch(...e),["prevent"])),class:"search-form"},["primary"===t.flowType?(v(),u("div",Me,[m("div",ke,[a[13]||(a[13]=m("label",{class:"form-label"},"Year",-1)),M(i,{modelValue:t.selectedYear,"onUpdate:modelValue":a[0]||(a[0]=e=>t.selectedYear=e),options:t.years,loading:t.loading&&!t.years.length,preloader:t.loadingYears,"state-loaded":t.stateLoadedYears,"auto-expand":!1,disabled:t.loadingYears&&0===t.years.length,placeholder:"Select Year",onChange:t.onYearChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",xe,[a[14]||(a[14]=m("label",{class:"form-label"},"Make",-1)),M(i,{modelValue:t.selectedMake,"onUpdate:modelValue":a[1]||(a[1]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&t.selectedYear&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,disabled:!t.selectedYear||t.loadingMakes||!t.stateLoadedMakes,placeholder:"Select Make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Ce,[a[15]||(a[15]=m("label",{class:"form-label"},"Model",-1)),M(i,{modelValue:t.selectedModel,"onUpdate:modelValue":a[2]||(a[2]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake||t.loadingModels||!t.stateLoadedModels,placeholder:"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Se,[a[16]||(a[16]=m("label",{class:"form-label"},"Modification",-1)),M(i,{modelValue:t.selectedModification,"onUpdate:modelValue":a[3]||(a[3]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedModel&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedModel||t.loadingModifications||!t.stateLoadedModifications,placeholder:"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled"])])])):"alternative"===t.flowType?(v(),u("div",Le,[m("div",Ve,[a[17]||(a[17]=m("label",{class:"form-label"},"Make",-1)),M(i,{modelValue:t.selectedMake,"onUpdate:modelValue":a[4]||(a[4]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,"auto-expand":!1,disabled:t.loadingMakes&&0===t.makes.length,placeholder:"Select Make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",_e,[a[18]||(a[18]=m("label",{class:"form-label"},"Model",-1)),M(i,{modelValue:t.selectedModel,"onUpdate:modelValue":a[5]||(a[5]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake||t.loadingModels||!t.stateLoadedModels,placeholder:"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Te,[a[19]||(a[19]=m("label",{class:"form-label"},"Generation",-1)),M(i,{modelValue:t.selectedGeneration,"onUpdate:modelValue":a[6]||(a[6]=e=>t.selectedGeneration=e),options:t.generations,loading:t.loading&&t.selectedModel&&!t.generations.length,preloader:t.loadingGenerations,"state-loaded":t.stateLoadedGenerations,disabled:!t.selectedModel||t.loadingGenerations||!t.stateLoadedGenerations,placeholder:"Select Generation",onChange:t.onGenerationChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Ae,[a[20]||(a[20]=m("label",{class:"form-label"},"Modification",-1)),M(i,{modelValue:t.selectedModification,"onUpdate:modelValue":a[7]||(a[7]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedGeneration&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedGeneration||t.loadingModifications||!t.stateLoadedModifications,placeholder:"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled"])])])):"year_select"===t.flowType?(v(),u("div",Ie,[m("div",De,[a[21]||(a[21]=m("label",{class:"form-label"},"Make",-1)),M(i,{modelValue:t.selectedMake,"onUpdate:modelValue":a[8]||(a[8]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,"auto-expand":!1,disabled:t.loadingMakes&&0===t.makes.length,placeholder:"Select Make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Fe,[a[22]||(a[22]=m("label",{class:"form-label"},"Model",-1)),M(i,{modelValue:t.selectedModel,"onUpdate:modelValue":a[9]||(a[9]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake||t.loadingModels||!t.stateLoadedModels,placeholder:"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",$e,[a[23]||(a[23]=m("label",{class:"form-label"},"Year",-1)),M(i,{modelValue:t.selectedYear,"onUpdate:modelValue":a[10]||(a[10]=e=>t.selectedYear=e),options:t.years,loading:t.loading&&t.selectedModel&&!t.years.length,preloader:t.loadingYears,"state-loaded":t.stateLoadedYears,disabled:!t.selectedModel||t.loadingYears||!t.stateLoadedYears,placeholder:"Select Year",onChange:t.onYearChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Oe,[a[24]||(a[24]=m("label",{class:"form-label"},"Modification",-1)),M(i,{modelValue:t.selectedModification,"onUpdate:modelValue":a[11]||(a[11]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedYear&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedYear||t.loadingModifications||!t.stateLoadedModifications,placeholder:"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled"])])])):x("",!0)],32),t.error?(v(),u("div",He,[m("p",Ye,_(t.error),1)])):x("",!0)])}],["__scopeId","data-v-da784654"]]),ResultsDisplay:R({name:"ResultsDisplay",setup(){const e=z(),{loading:a,results:l,config:t,outputTemplate:o}=F(e),n=i(()=>o.value&&o.value.trim().length>0),s=i(()=>{var e,a,l;return!(null==(l=null==(a=null==(e=t.value.widgetConfig)?void 0:e.blocks)?void 0:a.button_to_ws)?void 0:l.hide)}),r=i(()=>{var e;return`https://www.wheel-size.com${(null==(e=t.value.widgetConfig)?void 0:e.utm)||""}`});return{loading:a,results:l,hasCustomTemplate:n,renderCustomTemplate:e=>n.value?((e,a)=>{try{let l=e;return l=ze(l,a),l=Re(l,a),l=je(l,a),l}catch(l){return console.warn("Template rendering error:",l),`<div class="text-red-500 text-sm">Template error: ${l.message}</div>`}})(o.value,e):"",showWheelSizeButton:s,wheelSizeUrl:r}}},[["render",function(e,a,l,t,o,n){return v(),u("div",Ue,[t.loading?(v(),u("div",Pe,a[0]||(a[0]=[m("svg",{class:"spinner",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[m("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),m("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),m("span",{class:"ml-2"},"Loading results...",-1)]))):0===t.results.length?(v(),u("div",Ne,a[1]||(a[1]=[m("p",{class:"no-results-text"},"No results found. Please try different search criteria.",-1)]))):(v(),u("div",We,[t.hasCustomTemplate?(v(),u("div",Ze,[(v(!0),u(V,null,D(t.results,(e,a)=>(v(),u("div",{key:a,innerHTML:t.renderCustomTemplate(e)},null,8,Je))),128))])):(v(),u("div",qe,[(v(!0),u(V,null,D(t.results,(e,l)=>(v(),u("div",{class:"space-y-4",key:l},[m("h3",Xe,_(e.make.name)+" "+_(e.model.name)+" ("+_(e.start_year)+"-"+_(e.end_year)+") ",1),(v(!0),u(V,null,D(e.wheels,(e,l)=>(v(),u("div",{key:l,class:L(["p-3 theme-rounded-md border",e.is_stock?"border-indigo-400 bg-indigo-50":"border-gray-300"])},[m("div",Ke,_(e.is_stock?"OE option":"After-market option"),1),m("div",Qe,[m("div",null,[a[2]||(a[2]=m("span",{class:"text-gray-500"},"Front:",-1)),I(" "+_(e.front.tire)+" – "+_(e.front.rim),1)]),!e.showing_fp_only&&e.rear.tire?(v(),u("div",ea,[a[3]||(a[3]=m("span",{class:"text-gray-500"},"Rear:",-1)),I(" "+_(e.rear.tire)+" – "+_(e.rear.rim),1)])):x("",!0)])],2))),128))]))),128))])),t.showWheelSizeButton?(v(),u("div",aa,[m("a",{href:t.wheelSizeUrl,target:"_blank",rel:"noopener noreferrer",class:"rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50"}," See more at Wheel-Size.com ",8,la)])):x("",!0)]))])}],["__scopeId","data-v-9337e7bd"]])},setup(){const e=z(),a=window.FinderV2Config||{},l=a.theme||{},t=i(()=>e.results.length>0),o=i(()=>{var e;const a=[];return l.name&&a.push(`theme-${l.name.toLowerCase().replace(/\s+/g,"-")}`),(null==(e=l.effects)?void 0:e.hoverEffect)&&a.push(`hover-${l.effects.hoverEffect}`),a.join(" ")}),n=i(()=>{const e={};if(l.colors&&(e["--theme-primary"]=l.colors.primary,e["--theme-secondary"]=l.colors.secondary,e["--theme-accent"]=l.colors.accent,e["--theme-background"]=l.colors.background,e["--theme-text"]=l.colors.text,e["--theme-primary-rgb"]=s(l.colors.primary),e["--theme-secondary-rgb"]=s(l.colors.secondary),e["--theme-accent-rgb"]=s(l.colors.accent)),l.typography&&(e["--theme-font-family"]=l.typography.fontFamily,e["--theme-font-size"]=l.typography.fontSize,e["--theme-font-weight"]=l.typography.fontWeight,e["--theme-line-height"]=l.typography.lineHeight,e["--theme-letter-spacing"]=l.typography.letterSpacing),l.spacing&&(e["--theme-padding"]=l.spacing.padding,e["--theme-margin"]=l.spacing.margin),l.effects){e["--theme-border-radius"]=l.effects.borderRadius,e["--theme-border-width"]=l.effects.borderWidth,e["--theme-animation-speed"]=l.effects.animationSpeed;const a=l.effects.shadowIntensity,t={none:"none",light:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",medium:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",heavy:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"};e["--theme-shadow"]=t[a]||t.medium}return e});function s(e){if(!e)return"0, 0, 0";const a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return a?`${parseInt(a[1],16)}, ${parseInt(a[2],16)}, ${parseInt(a[3],16)}`:"0, 0, 0"}return w(()=>{if(e.initialize(a),l.colors||l.typography||l.spacing||l.effects){const e=document.documentElement;Object.entries(n.value).forEach(([a,l])=>{e.style.setProperty(a,l)})}}),{hasResults:t,themeClasses:o,themeStyles:n}}},[["render",function(e,a,l,t,o,n){const s=C("VehicleSearch"),i=C("ResultsDisplay");return v(),u("div",{class:L(["finder-v2-widget p-1",t.themeClasses]),"data-iframe-height":"",style:H(t.themeStyles)},[m("div",ta,[M(s)]),t.hasResults?(v(),u("div",oa,[M(i)])):x("",!0)],6)}],["__scopeId","data-v-ccdadc91"]]));function sa(){window.parent&&window.parent!==window&&window.parentIFrame&&window.parentIFrame.size()}na.use(E()),document.addEventListener("DOMContentLoaded",()=>{var e,a,l;d.defaults.headers.common["X-CSRF-TOKEN"]=(null==(e=window.FinderV2Config)?void 0:e.csrfToken)||"",console.log("CSRF token configured:",(null==(a=window.FinderV2Config)?void 0:a.csrfToken)||"NOT FOUND"),na.config.globalProperties.$config=window.FinderV2Config||{};const t=document.getElementById("finder-v2-app");t?(na.mount(t),(null==(l=window.FinderV2Config)?void 0:l.iframeResize)&&setTimeout(()=>{if(window.parentIFrame){sa();new MutationObserver(()=>{setTimeout(sa,50)}).observe(t,{childList:!0,subtree:!0,attributes:!0}),window.addEventListener("resize",sa)}},100)):console.error("Finder-v2 widget container not found")}),window.FinderV2App=na;
