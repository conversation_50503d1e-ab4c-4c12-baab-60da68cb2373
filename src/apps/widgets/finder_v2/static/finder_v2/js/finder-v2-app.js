var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,o=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,n=(e,n)=>{for(var s in n||(n={}))l.call(n,s)&&o(e,s,n[s]);if(a)for(var s of a(n))t.call(n,s)&&o(e,s,n[s]);return e},s=(e,a,l)=>new Promise((t,o)=>{var n=e=>{try{i(l.next(e))}catch(a){o(a)}},s=e=>{try{i(l.throw(e))}catch(a){o(a)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(n,s);i((l=l.apply(e,a)).next())});import{r as i,c as r,a as d,d as c,b as u,e as m,o as p,F as g,A as f,j as h,I as v,w as y,f as w,g as b,h as M,i as k,k as x,l as S,n as C,m as A,p as L,t as V,T as _,q as T,s as I,u as $,v as D,x as E,y as F,z as H,B as O,C as R,D as G,E as Y}from"./listbox-CQy4m7nO.js";function z(e){const a=i([]),l=i(!0),t=i(10),o=i(5),s=`finder_v2_search_history_${e}`,d=r(()=>{try{const e="__localStorage_test__";return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return console.warn("localStorage not available:",e),!1}});function c(){if(a.value=[],d.value)try{localStorage.removeItem(s)}catch(e){console.warn("Failed to clear search history:",e)}}function u(e){l.value=e,e||c(),v()}const m=r(()=>a.value.slice(0,o.value)),p=r(()=>a.value.length>o.value);function g(e){const a=[],l=e.options||{};function t(a,l){if(!a)return null;if("year"===l)return a.name||a.year||a.slug||a.id;if("make"===l||"model"===l){const e=a.name||a.slug||a.id;return"string"==typeof e?e.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" "):e}if("modification"===l){if(a.engine||a.trim||a.generation){const l=[];let t=a.name||"";if(("primary"===e.flowType||"year_select"===e.flowType)&&a.generation){const e=a.generation;if(e.name&&""!==e.name.trim()){const a=`${e.start}-${e.end}`;l.push(`${e.name} (${a})`)}else l.push(`${e.start}-${e.end}`)}if(a.engine){const e=[];a.engine.capacity&&e.push(a.engine.capacity),a.engine.type&&e.push(a.engine.type),a.engine.fuel&&e.push(a.engine.fuel),a.engine.power&&a.engine.power.hp&&e.push(`${a.engine.power.hp}HP`),e.length>0&&l.push(e.join(" "))}return l.length>0?`${t} ${l.join(" • ")}`.trim():t}return a.name||a.slug||a.id}if("generation"===l){if(a.year_ranges&&Array.isArray(a.year_ranges)){const e=a.year_ranges.join(", ");return a.name&&""!==a.name.trim()?`${a.name} (${e})`:e}return a.name||a.slug||a.id}return a.name||a.slug||a.id}if(e.year){const o=t(l.year,"year")||e.year;a.push(o)}if(e.make){const o=t(l.make,"make")||e.make;a.push(o)}if(e.model){const o=t(l.model,"model")||e.model;a.push(o)}if(e.modification){const o=t(l.modification,"modification")||e.modification;a.push(o)}if(e.generation&&"alternative"===e.flowType){const o=t(l.generation,"generation")||e.generation;a.push(`(${o})`)}return a.join(" ")||"Unknown Search"}function f(e){if(!e||"object"!=typeof e)return{};const a=n({},e);return a.parameters||(a.parameters={year:a.year||"",make:a.make||"",model:a.model||"",modification:a.modification||"",generation:a.generation||""}),a.timestamp&&"number"!=typeof a.timestamp?a.timestamp=Number(a.timestamp)||Date.now():a.timestamp||(a.timestamp=Date.now()),a.id||(a.id=`${a.timestamp}_${Math.random().toString(36).substr(2,9)}`),a}function h(e){return!(!e||"object"!=typeof e)&&(!!(e.id&&e.timestamp&&e.parameters)&&(!("number"!=typeof e.timestamp||e.timestamp<=0)&&!(!e.parameters||"object"!=typeof e.parameters)))}function v(){if(d.value)try{const e={version:"1.0",enabled:l.value,searches:a.value};localStorage.setItem(s,JSON.stringify(e))}catch(e){console.warn("Failed to save search history:",e),"QuotaExceededError"===e.name&&function(){try{const e=a.value.slice(0,Math.floor(a.value.length/2));a.value=e,v()}catch(e){console.warn("Failed to handle quota exceeded:",e),c()}}()}}return function(){if(console.log("useSearchHistory initialize called for:",s),!d.value)return console.log("localStorage not available"),void(l.value=!1);try{const e=localStorage.getItem(s);if(console.log("localStorage data for",s,":",e),e){const t=JSON.parse(e);if(console.log("Parsed data:",t),function(e){if(!e||"object"!=typeof e)return!1;if(!Array.isArray(e.searches))return!1;return!0}(t)){console.log("Data validation passed, setting searches:",t.searches),l.value=!1!==t.enabled;const e=(t.searches||[]).map(f).filter(h);e.sort((e,a)=>a.timestamp-e.timestamp),a.value=e,console.log("DIRECT STATE CHECK AFTER ASSIGNMENT:",{searchesValue:a.value,searchesLength:a.value.length,isEnabledValue:l.value,rawSearchesRef:a,rawEnabledRef:l}),v()}else c()}}catch(e){console.warn("Failed to load search history:",e),c()}}(),{searches:a,isEnabled:l,displaySearches:m,hasMoreSearches:p,maxItems:t,displayItems:o,isLocalStorageAvailable:d,addSearch:function(e){if(l.value&&d.value)try{const l=function(e){const a=Date.now();return{id:`${a}_${Math.random().toString(36).substr(2,9)}`,timestamp:a,description:g(e),flowType:e.flowType||"primary",parameters:{year:e.year||"",make:e.make||"",model:e.model||"",modification:e.modification||"",generation:e.generation||""},options:e.options||{}}}(e),o=a.value.findIndex(e=>{return a=e.parameters,t=l.parameters,["year","make","model","modification","generation"].every(e=>(a[e]||"")===(t[e]||""));var a,t});-1!==o&&a.value.splice(o,1),a.value.unshift(l),a.value.length>t.value&&(a.value=a.value.slice(0,t.value)),v()}catch(o){console.warn("Failed to add search to history:",o)}},removeSearch:function(e){if(l.value)try{const l=a.value.findIndex(a=>a.id===e);-1!==l&&(a.value.splice(l,1),v())}catch(t){console.warn("Failed to remove search from history:",t)}},getSearch:function(e){return a.value.find(a=>a.id===e)||null},updateSearchTimestamp:function(e){if(l.value)try{const l=a.value.findIndex(a=>a.id===e);if(-1!==l){const e=a.value.splice(l,1)[0];e.timestamp=Date.now(),a.value.unshift(e),v()}}catch(t){console.warn("Failed to update search timestamp:",t)}},clearHistory:c,setEnabled:u,configure:function(e){void 0!==e.maxItems&&(t.value=Math.max(1,Math.min(50,e.maxItems))),void 0!==e.displayItems&&(o.value=Math.max(1,Math.min(t.value,e.displayItems))),void 0!==e.enabled&&u(e.enabled),a.value.length>t.value&&(a.value=a.value.slice(0,t.value),v())},getRelativeTime:function(e){const a=Date.now()-e,l=Math.floor(a/1e3),t=Math.floor(l/60),o=Math.floor(t/60),n=Math.floor(o/24);return n>0?`${n} day${n>1?"s":""} ago`:o>0?`${o} hour${o>1?"s":""} ago`:t>0?`${t} minute${t>1?"s":""} ago`:"Just now"}}}d.defaults.paramsSerializer=e=>{const a=new URLSearchParams;return Object.entries(e).forEach(([e,l])=>{Array.isArray(l)?l.forEach(l=>{null!=l&&""!==l&&a.append(e,l)}):null!=l&&""!==l&&a.append(e,l)}),a.toString()};const j=c("finder",()=>{const e=i({}),a=i(!1),l=i(null),t=i([]),o=i("");let n=null;const c=i(!1),u=i(!1),m=i(!1),p=i(!1),g=i(!1),f=i(!1),h=i(!1),v=i(!1),y=i(!1),w=i(!1),b=i(new Map),M=i(""),k=i(""),x=i(""),S=i(""),C=i(""),A=i([]),L=i([]),V=i([]),_=i([]),T=i([]),I=r(()=>e.value.flowType||"primary"),$=r(()=>e.value.apiVersion||"v2"),D=r(()=>e.value.widgetResources||{});function E(e=null,a=null){return s(this,null,function*(){var t;try{c.value=!0,f.value=!1;const l={};e&&(l.make=e),a&&(l.model=a);const o=yield P("year",l);A.value=(null==(t=o.data)?void 0:t.data)||o.data||[],f.value=!0}catch(o){l.value=o.message}finally{c.value=!1}})}function F(e=null){return s(this,null,function*(){var a;try{u.value=!0,h.value=!1;const l=e?{year:e}:{};Object.assign(l,U());const t=yield P("make",l);L.value=(null==(a=t.data)?void 0:a.data)||t.data||[],h.value=!0}catch(t){l.value=t.message}finally{u.value=!1}})}function H(e,a=null){return s(this,null,function*(){var t;try{m.value=!0,v.value=!1;const l={make:e};a&&(l.year=a);const o=yield P("model",l);V.value=(null==(t=o.data)?void 0:t.data)||o.data||[],v.value=!0}catch(o){l.value=o.message}finally{m.value=!1}})}function O(e,a,t=null){return s(this,null,function*(){var o;try{g.value=!0,w.value=!1;const l={make:e,model:a};t&&("primary"===I.value||"year_select"===I.value?l.year=t:"alternative"===I.value&&(l.generation=t));const n=yield P("modification",l);_.value=(null==(o=n.data)?void 0:o.data)||n.data||[],w.value=!0}catch(n){l.value=n.message}finally{g.value=!1}})}function R(e,a){return s(this,null,function*(){var t;try{p.value=!0,y.value=!1;const l={make:e,model:a},o=yield P("generation",l);T.value=(null==(t=o.data)?void 0:t.data)||o.data||[],y.value=!0}catch(o){l.value=o.message}finally{p.value=!1}})}function G(){return s(this,null,function*(){var e;try{a.value=!0,l.value=null;const o={make:k.value,model:x.value};"primary"===I.value||"year_select"===I.value?(o.year=M.value,o.modification=S.value):"alternative"===I.value&&(o.generation=C.value,o.modification=S.value);const s=yield P("search_by_model",o);if(t.value=(null==(e=s.data)?void 0:e.data)||s.data||[],n&&t.value.length>0){const e=A.value.find(e=>e.slug===M.value||e.id===M.value),a=L.value.find(e=>e.slug===k.value||e.id===k.value),l=V.value.find(e=>e.slug===x.value||e.id===x.value),t=_.value.find(e=>e.slug===S.value||e.id===S.value),o=T.value.find(e=>e.slug===C.value||e.id===C.value),s={flowType:I.value,year:M.value,make:k.value,model:x.value,modification:S.value,generation:C.value,options:{year:e,make:a,model:l,modification:t,generation:o}};n.addSearch(s)}setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},100)}catch(o){l.value=o.message}finally{a.value=!1}})}function Y(){M.value="",k.value="",x.value="",S.value="",C.value="",V.value=[],_.value=[],T.value=[]}function j(){t.value=[],setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},50)}function U(){var a,l,t,o,n;const s=(null==(l=null==(a=e.value)?void 0:a.content)?void 0:l.filter)||(null==(t=e.value)?void 0:t.filter)||{},i=s.by||(null==(n=null==(o=e.value)?void 0:o.content)?void 0:n.by)||"",r=e=>"string"==typeof e?e:(null==e?void 0:e.slug)||(null==e?void 0:e.value)||"",d={};if("brands"===i&&Array.isArray(s.brands)&&s.brands.length){const e=s.brands.map(r).filter(Boolean);e.length&&(d.brands=e.join(","))}else if("brands_exclude"===i&&Array.isArray(s.brands_exclude)&&s.brands_exclude.length){const e=s.brands_exclude.map(r).filter(Boolean);e.length&&(d.brands_exclude=e.join(","))}return d}function B(){var a,l;const t=(null==(l=null==(a=e.value)?void 0:a.content)?void 0:l.regions)||[];return t.length?{region:t}:{}}function P(a){return s(this,arguments,function*(a,l={}){const t=function(e,a){const l=Object.keys(a).sort().reduce((e,l)=>(e[l]=a[l],e),{});return`${e}:${JSON.stringify(l)}`}(a,l);if(b.value.has(t))return yield b.value.get(t);const o=function(a){return s(this,arguments,function*(a,l={}){const t=D.value[a];if(!t||!t[1])throw new Error(`API endpoint not configured: ${a}`);let o=t[1];return o.startsWith("/")&&e.value.baseUrl&&(o=e.value.baseUrl+o),["make","model","year","generation","modification"].includes(a)&&Object.assign(l,B()),yield d.get(o,{params:l})})}(a,l);b.value.set(t,o);try{return yield o}finally{b.value.delete(t)}})}return{config:e,loading:a,error:l,results:t,outputTemplate:o,loadingYears:c,loadingMakes:u,loadingModels:m,loadingGenerations:p,loadingModifications:g,stateLoadedYears:f,stateLoadedMakes:h,stateLoadedModels:v,stateLoadedGenerations:y,stateLoadedModifications:w,selectedYear:M,selectedMake:k,selectedModel:x,selectedModification:S,selectedGeneration:C,years:A,makes:L,models:V,modifications:_,generations:T,flowType:I,apiVersion:$,widgetResources:D,initialize:function(t){var i;e.value=t,o.value=(null==(i=t.interface)?void 0:i.outputTemplate)||"",console.log("Widget config debug:",t);const r=t.id||t.uuid||t.widgetUuid||"default";console.log("Using widget ID for search history:",r),n=z(r);const d=t.search_history||{};Object.keys(d).length>0&&n.configure(d),function(){s(this,null,function*(){try{a.value=!0,l.value=null,"primary"===I.value?yield E():yield F()}catch(e){l.value=e.message}finally{a.value=!1}})}()},loadYears:E,loadMakes:F,loadModels:H,loadModifications:O,loadGenerations:R,searchByVehicle:G,resetVehicleSearch:Y,clearResults:j,buildBrandFilterParams:U,buildRegionParams:B,executeSearchFromHistory:function(e){return s(this,null,function*(){if(!n)return void console.warn("Search history not initialized");const a=n.getSearch(e);if(a)try{j(),yield function(e){return s(this,null,function*(){const a=e.parameters,l=e.flowType||"primary";Y();try{"primary"===l?(a.year&&(M.value=a.year,yield F(a.year)),a.make&&(k.value=a.make,yield H(a.make,a.year)),a.model&&(x.value=a.model,yield O(a.make,a.model,a.year)),a.modification&&(S.value=a.modification)):"alternative"===l?(a.make&&(k.value=a.make,yield H(a.make)),a.model&&(x.value=a.model,yield R(a.make,a.model)),a.generation&&(C.value=a.generation,yield O(a.make,a.model,a.generation)),a.modification&&(S.value=a.modification)):"year_select"===l&&(a.make&&(k.value=a.make,yield H(a.make)),a.model&&(x.value=a.model,yield E(a.make,a.model)),a.year&&(M.value=a.year,yield O(a.make,a.model,a.year)),a.modification&&(S.value=a.modification))}catch(t){throw console.error("Failed to populate form from search:",t),t}})}(a),n.updateSearchTimestamp(e),yield G()}catch(l){console.error("Failed to execute search from history:",l),l.value="Failed to execute search from history"}else console.warn("Search not found:",e)})},getSearchHistory:function(){return n}}});const U=(e,a)=>{const l=e.__vccOpts||e;for(const[t,o]of a)l[t]=o;return l},B={name:"CustomSelector",components:{Listbox:v,ListboxButton:h,ListboxOptions:f,ListboxOption:g,CheckIcon:function(e,a){return p(),u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z","clip-rule":"evenodd"})])},ChevronUpDownIcon:function(e,a){return p(),u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"fill-rule":"evenodd",d:"M10.53 3.47a.75.75 0 0 0-1.06 0L6.22 6.72a.75.75 0 0 0 1.06 1.06L10 5.06l2.72 2.72a.75.75 0 1 0 1.06-1.06l-3.25-3.25Zm-4.31 9.81 3.25 3.25a.75.75 0 0 0 1.06 0l3.25-3.25a.75.75 0 1 0-1.06-1.06L10 14.94l-2.72-2.72a.75.75 0 0 0-1.06 1.06Z","clip-rule":"evenodd"})])}},props:{modelValue:{type:[String,Number],default:""},options:{type:Array,default:()=>[]},placeholder:{type:String,default:"Select option"},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},preloader:{type:Boolean,default:!1},stateLoaded:{type:Boolean,default:!1},autoExpand:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(e,{emit:a}){const l=i(null),t=r({get:()=>e.modelValue,set:e=>{a("update:modelValue",e),a("change",e)}}),o=r(()=>e.modelValue?e.options.find(a=>(a.slug||a.id)===e.modelValue):null);y(()=>e.options,a=>{if(e.modelValue&&a.length>0){a.some(a=>(a.slug||a.id)===e.modelValue)||(t.value="")}});const n=window.matchMedia("(prefers-reduced-motion: reduce)").matches;y(()=>[e.stateLoaded,e.options.length],([a,t],[o])=>{var i,r;if(!o&&a&&t>0&&!e.disabled&&e.autoExpand&&!n){const a=document.activeElement,o=null!=(r=null==(i=l.value)?void 0:i.$el)?r:l.value;if(a&&a!==document.body&&o&&!o.contains(a)&&["INPUT","TEXTAREA"].includes(a.tagName))return;console.debug("AutoExpand →",{placeholder:e.placeholder,len:t}),setTimeout(()=>s(this,null,function*(){var a,t;yield C();const o=null!=(t=null==(a=l.value)?void 0:a.$el)?t:l.value;try{o&&"function"==typeof o.click&&(o.focus({preventScroll:!0}),o.click(),console.debug("AutoExpand click dispatched",{placeholder:e.placeholder}),d())}catch(n){console.warn("Auto-expand failed:",n)}}),200)}},{flush:"post"});const d=()=>{window.parentIFrame&&window.parentIFrame.size&&(window.parentIFrame.size(),setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},350))};return y(()=>e.modelValue,()=>{d()}),w(()=>{var e;l.value&&(null!=(e=l.value.$el)?e:l.value).addEventListener("click",d)}),b(()=>{var e;l.value&&(null!=(e=l.value.$el)?e:l.value).removeEventListener("click",d)}),{selectedValue:t,selectedOption:o,getDisplayText:e=>{if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const a=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?`${e.name}, ${a}`:a}return e.name||""},getDisplayData:e=>{var a,l,t,o,n,s;if(e.year_ranges&&Array.isArray(e.year_ranges)&&"number"==typeof e.start&&"number"==typeof e.end){const a=e.year_ranges.join(", ");return e.name&&""!==e.name.trim()?{isGeneration:!0,isModification:!1,name:e.name,yearRanges:a}:{isGeneration:!1,isModification:!1,name:a,yearRanges:""}}if(e.engine||e.trim||e.generation){const i=[];let r=e.name||"";if(("primary"===(null==(a=window.FinderV2Config)?void 0:a.flowType)||"primary"===(null==(t=null==(l=window.FinderV2Config)?void 0:l.widgetConfig)?void 0:t.flowType)||"year_select"===(null==(o=window.FinderV2Config)?void 0:o.flowType)||"year_select"===(null==(s=null==(n=window.FinderV2Config)?void 0:n.widgetConfig)?void 0:s.flowType))&&e.generation){const a=e.generation;let l="";if(a.name&&""!==a.name.trim()){const e=`${a.start}-${a.end}`;l=`${a.name} (${e})`}else l=`${a.start}-${a.end}`;l&&i.push(l)}if(e.trim&&""!==e.trim.trim()&&(e.trim,e.name),e.engine){const a=[];if(e.engine.capacity&&a.push(e.engine.capacity),e.engine.type&&a.push(e.engine.type),e.engine.fuel){let l=e.engine.fuel;"Petrol"===e.engine.fuel?l="Petrol":"Diesel"===e.engine.fuel?l="Diesel":"Hybrid"===e.engine.fuel&&(l="Hybrid"),a.push(l)}e.engine.power&&e.engine.power.hp&&a.push(`${e.engine.power.hp}HP`),a.length>0&&i.push(a.join(" "))}return{isGeneration:!1,isModification:!0,name:r,details:i.join(" • ")}}return{isGeneration:!1,isModification:!1,name:e.name||"",yearRanges:""}},buttonRef:l,triggerResize:d}}},P={class:"flex items-start"},N={class:"relative"},K={key:0,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},W={key:1,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},Z={class:"truncate"},J={class:"truncate text-ws-secondary-500"},q={key:1,class:"flex flex-col min-w-0"},X={class:"truncate font-medium"},Q={key:0,class:"truncate text-xs text-ws-secondary-500"},ee={key:2,class:"truncate"},ae={key:2,class:"col-start-1 row-start-1 flex w-full gap-2 pr-6"},le={class:"flex"},te={key:1,class:"flex flex-col min-w-0 w-full"},oe={class:"min-w-[32px] mt-2 ml-1"},ne={key:0,class:"spinner-external"};const se={key:0,class:"search-history-container"},ie={class:"search-history-header"},re=["aria-label","aria-expanded","aria-controls"],de={key:0,class:"search-count-badge"},ce=["id","aria-labelledby"],ue={class:"accordion-content"},me={key:0,class:"search-list"},pe=["onClick","onKeydown","aria-label"],ge={class:"search-content"},fe={class:"search-description"},he={class:"search-time"},ve=["onClick","onKeydown","aria-label"],ye={key:1,class:"show-more-container"},we={key:2,class:"show-less-container"},be={key:3,class:"empty-state"},Me={key:4,class:"clear-all-container"},ke={class:"confirmation-buttons"};const xe={class:"vehicle-search"},Se={key:0,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ce={class:"form-group"},Ae={class:"form-group"},Le={class:"form-group"},Ve={class:"form-group"},_e={key:1,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Te={class:"form-group"},Ie={class:"form-group"},$e={class:"form-group"},De={class:"form-group"},Ee={key:2,class:"form-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Fe={class:"form-group"},He={class:"form-group"},Oe={class:"form-group"},Re={class:"form-group"},Ge={key:0,class:"error-container"},Ye={class:"error"};const ze=e=>{const a=document.createElement("div");return a.textContent=e,a.innerHTML},je=(e,a)=>a.split(".").reduce((e,a)=>e&&void 0!==e[a]?e[a]:"",e),Ue=(e,a)=>e.replace(/\{\%\s*for\s+(\w+)\s+in\s+(\w+)\s*\%\}([\s\S]*?)\{\%\s*endfor\s*\%\}/g,(e,l,t,o)=>{const s=a[t];return Array.isArray(s)?s.map(e=>{let t=o;const s=n({[l]:e},a);return t=Be(t,s),t=Pe(t,s),t}).join(""):""}),Be=(e,a)=>{let l=e,t=!0;for(;t;){t=!1;const e=/\{\%\s*if\s+(.*?)\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?)(?:\{\%\s*else\s*\%\}((?:(?!\{\%\s*if\s)[\s\S])*?))?\{\%\s*endif\s*\%\}/g;l=l.replace(e,(e,l,o,n="")=>{t=!0;let s=Ne(l,a)?o:n;return s.includes("{% if")&&(s=Be(s,a)),s})}return l},Pe=(e,a)=>e.replace(/\{\{\s*(.*?)\s*\}\}/g,(e,l)=>{const t=l.trim(),o=t.match(/^(.+?)\s*\?\s*['"]?(.*?)['"]?\s*:\s*['"]?(.*?)['"]?$/);if(o){const[,e,l,t]=o,n=Ne(e.trim(),a),s=l.replace(/^['"]|['"]$/g,""),i=t.replace(/^['"]|['"]$/g,"");return ze(n?s:i)}const n=je(a,t);return ze(String(n||""))}),Ne=(e,a)=>{try{if(e.startsWith("not ")){const l=e.slice(4).trim();return!Ne(l,a)}const l=je(a,e.trim());return Boolean(l)}catch(l){return!1}},Ke={class:"results-display"},We={key:0,class:"loading"},Ze={key:1,class:"no-results"},Je={key:2,class:"results-content"},qe={key:0,class:"custom-results"},Xe=["innerHTML"],Qe={key:1,class:"default-results"},ea={class:"text-lg font-semibold mb-2"},aa={class:"font-medium uppercase tracking-wide text-sm mb-1"},la={class:"grid grid-cols-2 gap-2 text-sm"},ta={key:0},oa={key:2,class:"wheel-size-button-container"},na=["href"];const sa={class:"search-content"},ia={key:0,class:"results-section"};const ra=G(U({name:"FinderV2Widget",components:{VehicleSearch:U({name:"VehicleSearch",components:{CustomSelector:U(B,[["render",function(e,a,l,t,o,n){const s=S("ListboxButton"),i=S("ListboxOption"),r=S("CheckIcon"),d=S("ListboxOptions"),c=S("Listbox");return p(),u("div",P,[M(c,{modelValue:t.selectedValue,"onUpdate:modelValue":a[0]||(a[0]=e=>t.selectedValue=e),disabled:l.disabled,class:"flex-1"},{default:k(()=>[m("div",N,[M(s,{as:"button",class:A(["grid w-full cursor-default grid-cols-1 theme-rounded-md bg-white py-1.5 pr-2 pl-3 text-left text-gray-900 border border-gray-300 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-primary-color focus:border-primary-color sm:text-sm/6",l.disabled?"bg-gray-100 text-gray-500 cursor-not-allowed":""]),ref:"buttonRef"},{default:k(()=>[l.loading?(p(),u("span",K,a[1]||(a[1]=[m("svg",{class:"animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[m("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),m("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),m("span",{class:"truncate"},"Loading...",-1)]))):t.selectedOption?(p(),u("span",W,[t.getDisplayData(t.selectedOption).isGeneration?(p(),u(L,{key:0},[m("span",Z,V(t.getDisplayData(t.selectedOption).name),1),m("span",J,V(t.getDisplayData(t.selectedOption).yearRanges),1)],64)):t.getDisplayData(t.selectedOption).isModification?(p(),u("div",q,[m("span",X,V(t.getDisplayData(t.selectedOption).name),1),t.getDisplayData(t.selectedOption).details?(p(),u("span",Q,V(t.getDisplayData(t.selectedOption).details),1)):x("",!0)])):(p(),u("span",ee,V(t.getDisplayText(t.selectedOption)),1))])):(p(),u("span",ae,[m("span",{class:A(["truncate",l.disabled?"text-gray-500":"text-gray-900"])},V(l.placeholder),3)])),a[2]||(a[2]=m("svg",{class:"col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 sm:size-4",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true"},[m("path",{"fill-rule":"evenodd",d:"M5.22 10.22a.75.75 0 0 1 1.06 0L8 11.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 0 1 0-1.06ZM10.78 5.78a.75.75 0 0 1-1.06 0L8 4.06 6.28 5.78a.75.75 0 0 1-1.06-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06Z","clip-rule":"evenodd"})],-1))]),_:1,__:[2]},8,["class"]),M(_,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:k(()=>[M(d,{class:"z-10 mt-1 max-h-60 w-full overflow-auto theme-rounded-md bg-white py-1 text-base ring-1 shadow-lg ring-black/5 focus:outline-hidden sm:text-sm"},{default:k(()=>[l.options.length||l.loading?x("",!0):(p(),T(i,{key:0,value:null,class:"relative cursor-default select-none py-2 pr-9 pl-3 text-gray-500",disabled:""},{default:k(()=>a[3]||(a[3]=[I(" No options available ")])),_:1,__:[3]})),(p(!0),u(L,null,$(l.options,e=>(p(),T(i,{key:e.slug||e.id,value:e.slug||e.id,as:"template"},{default:k(({active:a,selected:l})=>[m("li",{class:A([a?"bg-primary-color text-white outline-hidden":"text-gray-900","relative cursor-default select-none py-2 pr-9 pl-3"])},[m("div",le,[t.getDisplayData(e).isGeneration?(p(),u(L,{key:0},[m("span",{class:A([l?"font-semibold":"font-normal","truncate"])},V(t.getDisplayData(e).name),3),m("span",{class:A([a?"text-white/80":"text-ws-secondary-500","ml-2 truncate"])},V(t.getDisplayData(e).yearRanges),3)],64)):t.getDisplayData(e).isModification?(p(),u("div",te,[m("span",{class:A([l?"font-semibold":"font-medium","truncate"])},V(t.getDisplayData(e).name),3),t.getDisplayData(e).details?(p(),u("span",{key:0,class:A([a?"text-white/80":"text-ws-secondary-500","truncate text-xs"])},V(t.getDisplayData(e).details),3)):x("",!0)])):(p(),u("span",{key:2,class:A([l?"font-semibold":"font-normal","truncate"])},V(t.getDisplayText(e)),3))]),l?(p(),u("span",{key:0,class:A([a?"text-white":"text-primary-color","absolute inset-y-0 right-0 flex items-center pr-4"])},[M(r,{class:"size-5","aria-hidden":"true"})],2)):x("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue","disabled"]),m("div",oe,[l.preloader?(p(),u("div",ne,a[4]||(a[4]=[m("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"w-5 h-5 text-gray-400"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)]))):x("",!0)])])}],["__scopeId","data-v-240841bf"]]),SearchHistoryIcon:U({name:"SearchHistoryIcon",setup(){const e=j(),{config:a}=D(e),l=i(!1),t=i(!1),o=i(!1),n=i(null),d=`search-history-accordion-${Math.random().toString(36).substr(2,9)}`,c=`search-history-button-${Math.random().toString(36).substr(2,9)}`;function u(){const a=e.getSearchHistory();console.log("SearchHistoryIcon updateSearchHistory:",a),n.value=a}w(()=>{u()}),y(()=>a.value,()=>{u()},{deep:!0}),y(()=>e.getSearchHistory(),e=>{console.log("SearchHistoryIcon watch getSearchHistory changed:",e),e&&e!==n.value&&(n.value=e)});const m=e=>O(e)?e.value:e,p=r(()=>{var e,a;const l=null==(e=n.value)?void 0:e.isEnabled;return null!=(a=m(l))&&a}),g=r(()=>{console.log("ALLSEARCHES COMPUTED START EVALUATION");const e=n.value;if(console.log("  - searchHistory.value:",e),!e)return[];const a=e.searches,l=m(a)||[];return console.log("  - searches value:",l,"length:",l.length),console.log("SearchHistoryIcon allSearches debug:",{searchHistory:n.value,searches:l,length:l.length,isEnabled:m(e.isEnabled)}),l});y(g,(e,a)=>{console.log("ALLSEARCHES WATCHER TRIGGERED:",{newLength:e.length,oldLength:a.length,newVal:e,oldVal:a})},{deep:!0});const f=r(()=>{var e,a;const l=null==(e=n.value)?void 0:e.displaySearches;return null!=(a=m(l))?a:[]}),h=r(()=>{var e,a;const l=null==(e=n.value)?void 0:e.hasMoreSearches;return null!=(a=m(l))&&a}),v=r(()=>g.value.length),b=r(()=>{var e,l;return!1!==(null==(l=null==(e=a.value)?void 0:e.search_history)?void 0:l.enabled)||p.value}),M=r(()=>t.value?g.value:f.value),k=r(()=>Math.max(0,g.value.length-f.value.length));function x(){window.parentIFrame&&window.parentIFrame.size&&(window.parentIFrame.size(),setTimeout(()=>{window.parentIFrame&&window.parentIFrame.size()},350))}function S(){l.value=!1,t.value=!1,x()}function C(){o.value=!1}function A(e){"Escape"===e.key&&(o.value?C():l.value&&S(),e.preventDefault())}return w(()=>{document.addEventListener("keydown",A)}),E(()=>{document.removeEventListener("keydown",A)}),{isAccordionOpen:l,showAll:t,showClearConfirmation:o,shouldShow:b,allSearches:g,displaySearches:f,hasMoreSearches:h,visibleSearches:M,remainingCount:k,searchCount:v,accordionId:d,buttonId:c,toggleAccordion:function(){l.value=!l.value,l.value&&(t.value=!1),x()},closeAccordion:S,executeSearch:function(a){return s(this,null,function*(){try{S(),yield e.executeSearchFromHistory(a),n.value=e.getSearchHistory(),setTimeout(()=>{const e=document.querySelector(".results-container");e&&e.scrollIntoView({behavior:"smooth",block:"start"})},100)}catch(l){console.error("Failed to execute search from history:",l)}})},removeSearch:function(e){n.value&&(n.value.removeSearch(e),0===g.value.length&&S())},confirmClearAll:function(){o.value=!0},clearAllHistory:function(){n.value&&(n.value.clearHistory(),o.value=!1,S())},cancelClearAll:C,getRelativeTime:function(e){var a;return(null==(a=n.value)?void 0:a.getRelativeTime(e))||""}}}},[["render",function(e,a,l,t,o,n){return t.shouldShow?(p(),u("div",se,[m("div",ie,[m("button",{onClick:a[0]||(a[0]=(...e)=>t.toggleAccordion&&t.toggleAccordion(...e)),"aria-label":`Search History (${t.searchCount} searches)`,"aria-expanded":t.isAccordionOpen,"aria-controls":t.accordionId,class:A(["history-toggle-button",{"has-history":t.searchCount>0,expanded:t.isAccordionOpen}])},[a[18]||(a[18]=m("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",class:"history-icon"},[m("path",{d:"M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3Z"})],-1)),t.searchCount>0?(p(),u("span",de,V(t.searchCount),1)):x("",!0)],10,re)]),M(_,{name:"accordion"},{default:k(()=>[t.isAccordionOpen?(p(),u("div",{key:0,id:t.accordionId,class:"search-history-accordion",role:"region","aria-labelledby":t.buttonId},[m("div",ue,[t.allSearches.length>0?(p(),u("div",me,[(p(!0),u(L,null,$(t.visibleSearches,e=>(p(),u("div",{key:e.id,class:"search-item",onClick:a=>t.executeSearch(e.id),onKeydown:[F(a=>t.executeSearch(e.id),["enter"]),F(H(a=>t.executeSearch(e.id),["prevent"]),["space"])],tabindex:"0",role:"button","aria-label":`Execute search for ${e.description}`},[m("div",ge,[m("div",fe,V(e.description),1),m("div",he,V(t.getRelativeTime(e.timestamp)),1)]),m("button",{class:"remove-button",onClick:H(a=>t.removeSearch(e.id),["stop"]),onKeydown:[F(H(a=>t.removeSearch(e.id),["stop"]),["enter"]),F(H(a=>t.removeSearch(e.id),["stop","prevent"]),["space"])],"aria-label":`Remove search for ${e.description}`,title:"Remove this search"}," ✕ ",40,ve)],40,pe))),128))])):x("",!0),t.hasMoreSearches&&!t.showAll?(p(),u("div",ye,[m("button",{class:"show-more-button",onClick:a[1]||(a[1]=e=>t.showAll=!0),onKeydown:[a[2]||(a[2]=F(e=>t.showAll=!0,["enter"])),a[3]||(a[3]=F(H(e=>t.showAll=!0,["prevent"]),["space"]))],"aria-label":"Show more search history"}," Show More ("+V(t.remainingCount)+" more) ",33)])):x("",!0),t.showAll&&t.hasMoreSearches?(p(),u("div",we,[m("button",{class:"show-less-button",onClick:a[4]||(a[4]=e=>t.showAll=!1),onKeydown:[a[5]||(a[5]=F(e=>t.showAll=!1,["enter"])),a[6]||(a[6]=F(H(e=>t.showAll=!1,["prevent"]),["space"]))],"aria-label":"Show less search history"}," Show Less ",32)])):x("",!0),0===t.allSearches.length?(p(),u("div",be,a[19]||(a[19]=[m("div",{class:"empty-icon"},"🔍",-1),m("p",{class:"empty-message"},"No search history yet",-1),m("p",{class:"empty-hint"},"Your recent searches will appear here",-1)]))):x("",!0),t.allSearches.length>0?(p(),u("div",Me,[m("button",{class:"clear-all-button",onClick:a[7]||(a[7]=(...e)=>t.confirmClearAll&&t.confirmClearAll(...e)),onKeydown:[a[8]||(a[8]=F((...e)=>t.confirmClearAll&&t.confirmClearAll(...e),["enter"])),a[9]||(a[9]=F(H((...e)=>t.confirmClearAll&&t.confirmClearAll(...e),["prevent"]),["space"]))],"aria-label":"Clear all search history"}," Clear All History ",32)])):x("",!0)])],8,ce)):x("",!0)]),_:1}),M(_,{name:"modal"},{default:k(()=>[t.showClearConfirmation?(p(),u("div",{key:0,class:"modal-overlay",onClick:a[17]||(a[17]=(...e)=>t.cancelClearAll&&t.cancelClearAll(...e))},[m("div",{class:"confirmation-modal",onClick:a[16]||(a[16]=H(()=>{},["stop"]))},[a[20]||(a[20]=m("h4",{class:"confirmation-title"},"Clear Search History",-1)),a[21]||(a[21]=m("p",{class:"confirmation-message"}," Are you sure you want to clear all your search history? This action cannot be undone. ",-1)),m("div",ke,[m("button",{class:"confirm-button",onClick:a[10]||(a[10]=(...e)=>t.clearAllHistory&&t.clearAllHistory(...e)),onKeydown:[a[11]||(a[11]=F((...e)=>t.clearAllHistory&&t.clearAllHistory(...e),["enter"])),a[12]||(a[12]=F(H((...e)=>t.clearAllHistory&&t.clearAllHistory(...e),["prevent"]),["space"]))]}," Clear All ",32),m("button",{class:"cancel-button",onClick:a[13]||(a[13]=(...e)=>t.cancelClearAll&&t.cancelClearAll(...e)),onKeydown:[a[14]||(a[14]=F((...e)=>t.cancelClearAll&&t.cancelClearAll(...e),["enter"])),a[15]||(a[15]=F(H((...e)=>t.cancelClearAll&&t.cancelClearAll(...e),["prevent"]),["space"]))]}," Cancel ",32)])])])):x("",!0)]),_:1})])):x("",!0)}],["__scopeId","data-v-b80df962"]])},setup(){const e=j(),{loading:a,error:l,selectedYear:t,selectedMake:o,selectedModel:n,selectedModification:d,selectedGeneration:c,years:u,makes:m,models:p,modifications:g,generations:f,flowType:h,loadingYears:v,loadingMakes:w,loadingModels:b,loadingGenerations:M,loadingModifications:k,stateLoadedYears:x,stateLoadedMakes:S,stateLoadedModels:C,stateLoadedGenerations:A,stateLoadedModifications:L}=D(e),V=r(()=>"primary"===h.value?t.value&&o.value&&n.value&&d.value:"alternative"===h.value?o.value&&n.value&&c.value&&d.value:"year_select"===h.value&&(o.value&&n.value&&t.value&&d.value)),_=i(!1);function T(){return s(this,null,function*(){V.value&&(yield e.searchByVehicle())})}return y(V,(e,a)=>s(this,null,function*(){e&&!a&&(_.value=!0,yield T(),setTimeout(()=>{_.value=!1},100))})),y(d,(e,l)=>s(this,null,function*(){!e||e===l||a.value||_.value||(yield T())})),{loading:a,error:l,selectedYear:t,selectedMake:o,selectedModel:n,selectedModification:d,selectedGeneration:c,years:u,makes:m,models:p,modifications:g,generations:f,flowType:h,loadingYears:v,loadingMakes:w,loadingModels:b,loadingGenerations:M,loadingModifications:k,stateLoadedYears:x,stateLoadedMakes:S,stateLoadedModels:C,stateLoadedGenerations:A,stateLoadedModifications:L,canSearch:V,onYearChange:function(){return s(this,null,function*(){"primary"===h.value?(e.selectedMake="",e.selectedModel="",e.selectedModification="",e.models=[],e.modifications=[],e.stateLoadedMakes=!1,e.stateLoadedModels=!1,e.stateLoadedModifications=!1,t.value&&(yield e.loadMakes(t.value))):"year_select"===h.value&&(e.selectedModification="",e.modifications=[],e.stateLoadedModifications=!1,t.value&&(yield e.loadModifications(o.value,n.value,t.value)))})},onMakeChange:function(){return s(this,null,function*(){e.selectedModel="",e.selectedModification="",e.selectedGeneration="",e.models=[],e.modifications=[],e.generations=[],e.stateLoadedModels=!1,e.stateLoadedModifications=!1,e.stateLoadedGenerations=!1,o.value&&("primary"===h.value?yield e.loadModels(o.value,t.value):"alternative"!==h.value&&"year_select"!==h.value||(yield e.loadModels(o.value)))})},onModelChange:function(){return s(this,null,function*(){e.selectedModification="",e.selectedGeneration="",e.modifications=[],e.generations=[],e.stateLoadedModifications=!1,e.stateLoadedGenerations=!1,"year_select"===h.value&&(e.selectedYear="",e.years=[],e.stateLoadedYears=!1),n.value&&("primary"===h.value?yield e.loadModifications(o.value,n.value,t.value):"alternative"===h.value?yield e.loadGenerations(o.value,n.value):"year_select"===h.value&&(yield e.loadYears(o.value,n.value)))})},onGenerationChange:function(){return s(this,null,function*(){e.selectedModification="",e.modifications=[],e.stateLoadedModifications=!1,c.value&&(yield e.loadModifications(o.value,n.value,c.value))})},handleSearch:T}}},[["render",function(e,a,l,t,o,n){const s=S("SearchHistoryIcon"),i=S("CustomSelector");return p(),u("div",xe,[M(s),m("form",{onSubmit:a[12]||(a[12]=H((...e)=>t.handleSearch&&t.handleSearch(...e),["prevent"])),class:"search-form"},["primary"===t.flowType?(p(),u("div",Se,[m("div",Ce,[a[13]||(a[13]=m("label",{class:"form-label"},"Year",-1)),M(i,{modelValue:t.selectedYear,"onUpdate:modelValue":a[0]||(a[0]=e=>t.selectedYear=e),options:t.years,loading:t.loading&&!t.years.length,preloader:t.loadingYears,"state-loaded":t.stateLoadedYears,"auto-expand":!1,disabled:t.loadingYears&&0===t.years.length,placeholder:"Select Year",onChange:t.onYearChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Ae,[a[14]||(a[14]=m("label",{class:"form-label"},"Make",-1)),M(i,{modelValue:t.selectedMake,"onUpdate:modelValue":a[1]||(a[1]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&t.selectedYear&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,disabled:!t.selectedYear||t.loadingMakes||!t.stateLoadedMakes,placeholder:"Select Make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Le,[a[15]||(a[15]=m("label",{class:"form-label"},"Model",-1)),M(i,{modelValue:t.selectedModel,"onUpdate:modelValue":a[2]||(a[2]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake||t.loadingModels||!t.stateLoadedModels,placeholder:"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Ve,[a[16]||(a[16]=m("label",{class:"form-label"},"Modification",-1)),M(i,{modelValue:t.selectedModification,"onUpdate:modelValue":a[3]||(a[3]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedModel&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedModel||t.loadingModifications||!t.stateLoadedModifications,placeholder:"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled"])])])):"alternative"===t.flowType?(p(),u("div",_e,[m("div",Te,[a[17]||(a[17]=m("label",{class:"form-label"},"Make",-1)),M(i,{modelValue:t.selectedMake,"onUpdate:modelValue":a[4]||(a[4]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,"auto-expand":!1,disabled:t.loadingMakes&&0===t.makes.length,placeholder:"Select Make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Ie,[a[18]||(a[18]=m("label",{class:"form-label"},"Model",-1)),M(i,{modelValue:t.selectedModel,"onUpdate:modelValue":a[5]||(a[5]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake||t.loadingModels||!t.stateLoadedModels,placeholder:"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",$e,[a[19]||(a[19]=m("label",{class:"form-label"},"Generation",-1)),M(i,{modelValue:t.selectedGeneration,"onUpdate:modelValue":a[6]||(a[6]=e=>t.selectedGeneration=e),options:t.generations,loading:t.loading&&t.selectedModel&&!t.generations.length,preloader:t.loadingGenerations,"state-loaded":t.stateLoadedGenerations,disabled:!t.selectedModel||t.loadingGenerations||!t.stateLoadedGenerations,placeholder:"Select Generation",onChange:t.onGenerationChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",De,[a[20]||(a[20]=m("label",{class:"form-label"},"Modification",-1)),M(i,{modelValue:t.selectedModification,"onUpdate:modelValue":a[7]||(a[7]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedGeneration&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedGeneration||t.loadingModifications||!t.stateLoadedModifications,placeholder:"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled"])])])):"year_select"===t.flowType?(p(),u("div",Ee,[m("div",Fe,[a[21]||(a[21]=m("label",{class:"form-label"},"Make",-1)),M(i,{modelValue:t.selectedMake,"onUpdate:modelValue":a[8]||(a[8]=e=>t.selectedMake=e),options:t.makes,loading:t.loading&&!t.makes.length,preloader:t.loadingMakes,"state-loaded":t.stateLoadedMakes,"auto-expand":!1,disabled:t.loadingMakes&&0===t.makes.length,placeholder:"Select Make",onChange:t.onMakeChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",He,[a[22]||(a[22]=m("label",{class:"form-label"},"Model",-1)),M(i,{modelValue:t.selectedModel,"onUpdate:modelValue":a[9]||(a[9]=e=>t.selectedModel=e),options:t.models,loading:t.loading&&t.selectedMake&&!t.models.length,preloader:t.loadingModels,"state-loaded":t.stateLoadedModels,disabled:!t.selectedMake||t.loadingModels||!t.stateLoadedModels,placeholder:"Select Model",onChange:t.onModelChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Oe,[a[23]||(a[23]=m("label",{class:"form-label"},"Year",-1)),M(i,{modelValue:t.selectedYear,"onUpdate:modelValue":a[10]||(a[10]=e=>t.selectedYear=e),options:t.years,loading:t.loading&&t.selectedModel&&!t.years.length,preloader:t.loadingYears,"state-loaded":t.stateLoadedYears,disabled:!t.selectedModel||t.loadingYears||!t.stateLoadedYears,placeholder:"Select Year",onChange:t.onYearChange},null,8,["modelValue","options","loading","preloader","state-loaded","disabled","onChange"])]),m("div",Re,[a[24]||(a[24]=m("label",{class:"form-label"},"Modification",-1)),M(i,{modelValue:t.selectedModification,"onUpdate:modelValue":a[11]||(a[11]=e=>t.selectedModification=e),options:t.modifications,loading:t.loading&&t.selectedYear&&!t.modifications.length,preloader:t.loadingModifications,"state-loaded":t.stateLoadedModifications,disabled:!t.selectedYear||t.loadingModifications||!t.stateLoadedModifications,placeholder:"Select Modification"},null,8,["modelValue","options","loading","preloader","state-loaded","disabled"])])])):x("",!0)],32),t.error?(p(),u("div",Ge,[m("p",Ye,V(t.error),1)])):x("",!0)])}],["__scopeId","data-v-ffbda395"]]),ResultsDisplay:U({name:"ResultsDisplay",setup(){const e=j(),{loading:a,results:l,config:t,outputTemplate:o}=D(e),n=r(()=>o.value&&o.value.trim().length>0),s=r(()=>{var e,a,l;return!(null==(l=null==(a=null==(e=t.value.widgetConfig)?void 0:e.blocks)?void 0:a.button_to_ws)?void 0:l.hide)}),i=r(()=>{var e;return`https://www.wheel-size.com${(null==(e=t.value.widgetConfig)?void 0:e.utm)||""}`});return{loading:a,results:l,hasCustomTemplate:n,renderCustomTemplate:e=>n.value?((e,a)=>{try{let l=e;return l=Ue(l,a),l=Be(l,a),l=Pe(l,a),l}catch(l){return console.warn("Template rendering error:",l),`<div class="text-red-500 text-sm">Template error: ${l.message}</div>`}})(o.value,e):"",showWheelSizeButton:s,wheelSizeUrl:i}}},[["render",function(e,a,l,t,o,n){return p(),u("div",Ke,[t.loading?(p(),u("div",We,a[0]||(a[0]=[m("svg",{class:"spinner",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[m("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),m("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),m("span",{class:"ml-2"},"Loading results...",-1)]))):0===t.results.length?(p(),u("div",Ze,a[1]||(a[1]=[m("p",{class:"no-results-text"},"No results found. Please try different search criteria.",-1)]))):(p(),u("div",Je,[t.hasCustomTemplate?(p(),u("div",qe,[(p(!0),u(L,null,$(t.results,(e,a)=>(p(),u("div",{key:a,innerHTML:t.renderCustomTemplate(e)},null,8,Xe))),128))])):(p(),u("div",Qe,[(p(!0),u(L,null,$(t.results,(e,l)=>(p(),u("div",{class:"space-y-4",key:l},[m("h3",ea,V(e.make.name)+" "+V(e.model.name)+" ("+V(e.start_year)+"-"+V(e.end_year)+") ",1),(p(!0),u(L,null,$(e.wheels,(e,l)=>(p(),u("div",{key:l,class:A(["p-3 theme-rounded-md border",e.is_stock?"border-indigo-400 bg-indigo-50":"border-gray-300"])},[m("div",aa,V(e.is_stock?"OE option":"After-market option"),1),m("div",la,[m("div",null,[a[2]||(a[2]=m("span",{class:"text-gray-500"},"Front:",-1)),I(" "+V(e.front.tire)+" – "+V(e.front.rim),1)]),!e.showing_fp_only&&e.rear.tire?(p(),u("div",ta,[a[3]||(a[3]=m("span",{class:"text-gray-500"},"Rear:",-1)),I(" "+V(e.rear.tire)+" – "+V(e.rear.rim),1)])):x("",!0)])],2))),128))]))),128))])),t.showWheelSizeButton?(p(),u("div",oa,[m("a",{href:t.wheelSizeUrl,target:"_blank",rel:"noopener noreferrer",class:"rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset hover:bg-gray-50"}," See more at Wheel-Size.com ",8,na)])):x("",!0)]))])}],["__scopeId","data-v-9337e7bd"]])},setup(){const e=j(),a=window.FinderV2Config||{},l=a.theme||{},t=r(()=>e.results.length>0),o=r(()=>{var e;const a=[];return l.name&&a.push(`theme-${l.name.toLowerCase().replace(/\s+/g,"-")}`),(null==(e=l.effects)?void 0:e.hoverEffect)&&a.push(`hover-${l.effects.hoverEffect}`),a.join(" ")}),n=r(()=>{const e={};if(l.colors&&(e["--theme-primary"]=l.colors.primary,e["--theme-secondary"]=l.colors.secondary,e["--theme-accent"]=l.colors.accent,e["--theme-background"]=l.colors.background,e["--theme-text"]=l.colors.text,e["--theme-primary-rgb"]=s(l.colors.primary),e["--theme-secondary-rgb"]=s(l.colors.secondary),e["--theme-accent-rgb"]=s(l.colors.accent)),l.typography&&(e["--theme-font-family"]=l.typography.fontFamily,e["--theme-font-size"]=l.typography.fontSize,e["--theme-font-weight"]=l.typography.fontWeight,e["--theme-line-height"]=l.typography.lineHeight,e["--theme-letter-spacing"]=l.typography.letterSpacing),l.spacing&&(e["--theme-padding"]=l.spacing.padding,e["--theme-margin"]=l.spacing.margin),l.effects){e["--theme-border-radius"]=l.effects.borderRadius,e["--theme-border-width"]=l.effects.borderWidth,e["--theme-animation-speed"]=l.effects.animationSpeed;const a=l.effects.shadowIntensity,t={none:"none",light:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",medium:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",heavy:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"};e["--theme-shadow"]=t[a]||t.medium}return e});function s(e){if(!e)return"0, 0, 0";const a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return a?`${parseInt(a[1],16)}, ${parseInt(a[2],16)}, ${parseInt(a[3],16)}`:"0, 0, 0"}return w(()=>{if(e.initialize(a),l.colors||l.typography||l.spacing||l.effects){const e=document.documentElement;Object.entries(n.value).forEach(([a,l])=>{e.style.setProperty(a,l)})}}),{hasResults:t,themeClasses:o,themeStyles:n}}},[["render",function(e,a,l,t,o,n){const s=S("VehicleSearch"),i=S("ResultsDisplay");return p(),u("div",{class:A(["finder-v2-widget p-1",t.themeClasses]),"data-iframe-height":"",style:R(t.themeStyles)},[m("div",sa,[M(s)]),t.hasResults?(p(),u("div",ia,[M(i)])):x("",!0)],6)}],["__scopeId","data-v-ccdadc91"]]));function da(){window.parent&&window.parent!==window&&window.parentIFrame&&window.parentIFrame.size()}ra.use(Y()),document.addEventListener("DOMContentLoaded",()=>{var e,a,l;d.defaults.headers.common["X-CSRF-TOKEN"]=(null==(e=window.FinderV2Config)?void 0:e.csrfToken)||"",console.log("CSRF token configured:",(null==(a=window.FinderV2Config)?void 0:a.csrfToken)||"NOT FOUND"),ra.config.globalProperties.$config=window.FinderV2Config||{};const t=document.getElementById("finder-v2-app");t?(ra.mount(t),(null==(l=window.FinderV2Config)?void 0:l.iframeResize)&&setTimeout(()=>{if(window.parentIFrame){da();new MutationObserver(()=>{setTimeout(da,50)}).observe(t,{childList:!0,subtree:!0,attributes:!0}),window.addEventListener("resize",da)}},100)):console.error("Finder-v2 widget container not found")}),window.FinderV2App=ra;
