var e=Object.defineProperty,t=Object.defineProperties,o=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,a=(t,o,n)=>o in t?e(t,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[o]=n,i=(e,t)=>{for(var o in t||(t={}))r.call(t,o)&&a(e,o,t[o]);if(n)for(var o of n(t))l.call(t,o)&&a(e,o,t[o]);return e},s=(e,n)=>t(e,o(n)),u=(e,t)=>{var o={};for(var a in e)r.call(e,a)&&t.indexOf(a)<0&&(o[a]=e[a]);if(null!=e&&n)for(var a of n(e))t.indexOf(a)<0&&l.call(e,a)&&(o[a]=e[a]);return o};import{c as d,E as c,G as v,w as h,H as p,J as w,h as m,K as f,L as g,M as k,r as x,N as b,O as M,P as C,Q as I,R as A,S as B,g as y,U as S,V as j,W as V,X as Z,Y as H,Z as P,_ as O,q as L,$ as T,a0 as E,a1 as R,a2 as D,a3 as F,a4 as N,n as $,a5 as z,a6 as U,a7 as G,a8 as W,a9 as q,aa as _,ab as K,ac as Y,ad as X,ae as Q,af as J,ag as ee,ah as te,ai as oe,aj as ne,ak as re,al as le,am as ae,an as ie,ao as se,ap as ue,aq as de,ar as ce,as as ve,at as he,au as pe,av as we,p as me,I as fe,j as ge,aw as ke,F as xe,A as be,ax as Me,e as Ce,o as Ie,f as Ae,D as Be,C as ye,b as Se}from"./listbox-B6xKsyeJ.js";function je(e,t,o){var n;let r,l=null!=(n=o.initialDeps)?n:[];function a(){var n,a,i,s;let u;o.key&&(null==(n=o.debug)?void 0:n.call(o))&&(u=Date.now());const d=e();if(!(d.length!==l.length||d.some((e,t)=>l[t]!==e)))return r;let c;if(l=d,o.key&&(null==(a=o.debug)?void 0:a.call(o))&&(c=Date.now()),r=t(...d),o.key&&(null==(i=o.debug)?void 0:i.call(o))){const e=Math.round(100*(Date.now()-u))/100,t=Math.round(100*(Date.now()-c))/100,n=t/16,r=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${r(t,5)} /${r(e,5)} ms`,`\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0,Math.min(120-120*n,120))}deg 100% 31%);`,null==o?void 0:o.key)}return null==(s=null==o?void 0:o.onChange)||s.call(o,r),r}return a.updateDeps=e=>{l=e},a}function Ve(e,t){if(void 0===e)throw new Error("Unexpected undefined");return e}const Ze=(e,t,o)=>{let n;return function(...r){e.clearTimeout(n),n=e.setTimeout(()=>t.apply(this,r),o)}},He=e=>{const{offsetWidth:t,offsetHeight:o}=e;return{width:t,height:o}},Pe=e=>e,Oe=e=>{const t=Math.max(e.startIndex-e.overscan,0),o=Math.min(e.endIndex+e.overscan,e.count-1),n=[];for(let r=t;r<=o;r++)n.push(r);return n},Le=(e,t)=>{const o=e.scrollElement;if(!o)return;const n=e.targetWindow;if(!n)return;const r=e=>{const{width:o,height:n}=e;t({width:Math.round(o),height:Math.round(n)})};if(r(He(o)),!n.ResizeObserver)return()=>{};const l=new n.ResizeObserver(t=>{const n=()=>{const e=t[0];if(null==e?void 0:e.borderBoxSize){const t=e.borderBoxSize[0];if(t)return void r({width:t.inlineSize,height:t.blockSize})}r(He(o))};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(n):n()});return l.observe(o,{box:"border-box"}),()=>{l.unobserve(o)}},Te={passive:!0},Ee="undefined"==typeof window||"onscrollend"in window,Re=(e,t)=>{const o=e.scrollElement;if(!o)return;const n=e.targetWindow;if(!n)return;let r=0;const l=e.options.useScrollendEvent&&Ee?()=>{}:Ze(n,()=>{t(r,!1)},e.options.isScrollingResetDelay),a=n=>()=>{const{horizontal:a,isRtl:i}=e.options;r=a?o.scrollLeft*(i?-1:1):o.scrollTop,l(),t(r,n)},i=a(!0),s=a(!1);s(),o.addEventListener("scroll",i,Te);const u=e.options.useScrollendEvent&&Ee;return u&&o.addEventListener("scrollend",s,Te),()=>{o.removeEventListener("scroll",i),u&&o.removeEventListener("scrollend",s)}},De=(e,t,o)=>{if(null==t?void 0:t.borderBoxSize){const e=t.borderBoxSize[0];if(e){return Math.round(e[o.options.horizontal?"inlineSize":"blockSize"])}}return e[o.options.horizontal?"offsetWidth":"offsetHeight"]},Fe=(e,{adjustments:t=0,behavior:o},n)=>{var r,l;const a=e+t;null==(l=null==(r=n.scrollElement)?void 0:r.scrollTo)||l.call(r,{[n.options.horizontal?"left":"top"]:a,behavior:o})};class Ne{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null;const t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver(e=>{e.forEach(e=>{const t=()=>{this._measureElement(e.target,e)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()})}):null);return{disconnect:()=>{var o;null==(o=t())||o.disconnect(),e=null},observe:e=>{var o;return null==(o=t())?void 0:o.observe(e,{box:"border-box"})},unobserve:e=>{var o;return null==(o=t())?void 0:o.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach(([t,o])=>{void 0===o&&delete e[t]}),this.options=i({debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:Pe,rangeExtractor:Oe,onChange:()=>{},measureElement:De,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1},e)},this.notify=e=>{var t,o;null==(o=(t=this.options).onChange)||o.call(t,this,e)},this.maybeNotify=je(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),e=>{this.notify(e)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(e=>e()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e,t;const o=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==o){if(this.cleanup(),!o)return void this.maybeNotify();this.scrollElement=o,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=null!=(e=null==(t=this.scrollElement)?void 0:t.window)?e:null,this.elementsCache.forEach(e=>{this.observer.observe(e)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,e=>{this.scrollRect=e,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()}))}},this.getSize=()=>{var e;return this.options.enabled?(this.scrollRect=null!=(e=this.scrollRect)?e:this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0)},this.getScrollOffset=()=>{var e;return this.options.enabled?(this.scrollOffset=null!=(e=this.scrollOffset)?e:"function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset,this.scrollOffset):(this.scrollOffset=null,0)},this.getFurthestMeasurement=(e,t)=>{const o=new Map,n=new Map;for(let r=t-1;r>=0;r--){const t=e[r];if(o.has(t.lane))continue;const l=n.get(t.lane);if(null==l||t.end>l.end?n.set(t.lane,t):t.end<l.end&&o.set(t.lane,!0),o.size===this.options.lanes)break}return n.size===this.options.lanes?Array.from(n.values()).sort((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end)[0]:void 0},this.getMeasurementOptions=je(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(e,t,o,n,r)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:o,getItemKey:n,enabled:r}),{key:!1}),this.getMeasurements=je(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:e,paddingStart:t,scrollMargin:o,getItemKey:n,enabled:r},l)=>{if(!r)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(e=>{this.itemSizeCache.set(e.key,e.size)}));const a=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const i=this.measurementsCache.slice(0,a);for(let s=a;s<e;s++){const e=n(s),r=1===this.options.lanes?i[s-1]:this.getFurthestMeasurement(i,s),a=r?r.end+this.options.gap:t+o,u=l.get(e),d="number"==typeof u?u:this.options.estimateSize(s),c=a+d,v=r?r.lane:s%this.options.lanes;i[s]={index:s,start:a,size:d,end:c,key:e,lane:v}}return this.measurementsCache=i,i},{key:!1,debug:()=>this.options.debug}),this.calculateRange=je(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(e,t,o,n)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:o,lanes:n}){const r=e.length-1,l=t=>e[t].start;if(e.length<=n)return{startIndex:0,endIndex:r};let a=$e(0,r,l,o),i=a;if(1===n)for(;i<r&&e[i].end<o+t;)i++;else if(n>1){const l=Array(n).fill(0);for(;i<r&&l.some(e=>e<o+t);){const t=e[i];l[t.lane]=t.end,i++}const s=Array(n).fill(o+t);for(;a>=0&&s.some(e=>e>=o);){const t=e[a];s[t.lane]=t.start,a--}a=Math.max(0,a-a%n),i=Math.min(r,i+(n-1-i%n))}return{startIndex:a,endIndex:i}}({measurements:e,outerSize:t,scrollOffset:o,lanes:n}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=je(()=>{let e=null,t=null;const o=this.calculateRange();return o&&(e=o.startIndex,t=o.endIndex),this.maybeNotify.updateDeps([this.isScrolling,e,t]),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,t]},(e,t,o,n,r)=>null===n||null===r?[]:e({startIndex:n,endIndex:r,overscan:t,count:o}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{const t=this.options.indexAttribute,o=e.getAttribute(t);return o?parseInt(o,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{const o=this.indexFromElement(e),n=this.measurementsCache[o];if(!n)return;const r=n.key,l=this.elementsCache.get(r);l!==e&&(l&&this.observer.unobserve(l),this.observer.observe(e),this.elementsCache.set(r,e)),e.isConnected&&this.resizeItem(o,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{var o;const n=this.measurementsCache[e];if(!n)return;const r=t-(null!=(o=this.itemSizeCache.get(n.key))?o:n.size);0!==r&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(n,r,this):"backward"===this.scrollDirection&&n.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=r,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(n.index),this.itemSizeCache=new Map(this.itemSizeCache.set(n.key,t)),this.notify(!1))},this.measureElement=e=>{e?this._measureElement(e,void 0):this.elementsCache.forEach((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))})},this.getVirtualItems=je(()=>[this.getVirtualIndexes(),this.getMeasurements()],(e,t)=>{const o=[];for(let n=0,r=e.length;n<r;n++){const r=t[e[n]];o.push(r)}return o},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{const t=this.getMeasurements();if(0!==t.length)return Ve(t[$e(0,t.length-1,e=>Ve(t[e]).start,e)])},this.getOffsetForAlignment=(e,t,o=0)=>{const n=this.getSize(),r=this.getScrollOffset();"auto"===t&&(t=e>=r+n?"end":"start"),"center"===t?e+=(o-n)/2:"end"===t&&(e-=n);const l=this.getTotalSize()-n;return Math.max(Math.min(l,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));const o=this.measurementsCache[e];if(!o)return;const n=this.getSize(),r=this.getScrollOffset();if("auto"===t)if(o.end>=r+n-this.options.scrollPaddingEnd)t="end";else{if(!(o.start<=r+this.options.scrollPaddingStart))return[r,t];t="start"}const l="end"===t?o.end+this.options.scrollPaddingEnd:o.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(l,t,o.size),t]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:o}={})=>{this.cancelScrollToIndex(),"smooth"===o&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:o})},this.scrollToIndex=(e,{align:t="auto",behavior:o}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===o&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const n=this.getOffsetForIndex(e,t);if(!n)return;const[r,l]=n;this._scrollToOffset(r,{adjustments:void 0,behavior:o}),"smooth"!==o&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{this.scrollToIndexTimeoutId=null;if(this.elementsCache.has(this.options.getItemKey(e))){const r=this.getOffsetForIndex(e,l);if(!r)return;const[a]=r,i=this.getScrollOffset();t=a,n=i,Math.abs(t-n)<=1||this.scrollToIndex(e,{align:l,behavior:o})}else this.scrollToIndex(e,{align:l,behavior:o});var t,n}))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e,t;const o=this.getMeasurements();let n;if(0===o.length)n=this.options.paddingStart;else if(1===this.options.lanes)n=null!=(e=null==(t=o[o.length-1])?void 0:t.end)?e:0;else{const e=Array(this.options.lanes).fill(null);let t=o.length-1;for(;t>=0&&e.some(e=>null===e);){const n=o[t];null===e[n.lane]&&(e[n.lane]=n.end),t--}n=Math.max(...e.filter(e=>null!==e))}return Math.max(n-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:o})=>{this.options.scrollToFn(e,{behavior:o,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}const $e=(e,t,o,n)=>{for(;e<=t;){const r=(e+t)/2|0,l=o(r);if(l<n)e=r+1;else{if(!(l>n))return r;t=r-1}}return e>0?e-1:0};function ze(e){return function(e){const t=new Ne(c(e)),o=v(t),n=t._didMount();return h(()=>c(e).getScrollElement(),e=>{e&&t._willUpdate()},{immediate:!0}),h(()=>c(e),e=>{t.setOptions(s(i({},e),{onChange:(t,n)=>{var r;w(o),null==(r=e.onChange)||r.call(e,t,n)}})),t._willUpdate(),w(o)},{immediate:!0}),p(n),o}(d(()=>i({observeElementRect:Le,observeElementOffset:Re,scrollToFn:Fe},c(e))))}function Ue(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}function Ge(){let e=[],t={addEventListener:(e,o,n,r)=>(e.addEventListener(o,n,r),t.add(()=>e.removeEventListener(o,n,r))),requestAnimationFrame(...e){let o=requestAnimationFrame(...e);t.add(()=>cancelAnimationFrame(o))},nextFrame(...e){t.requestAnimationFrame(()=>{t.requestAnimationFrame(...e)})},setTimeout(...e){let o=setTimeout(...e);t.add(()=>clearTimeout(o))},microTask(...e){let o={current:!0};return Ue(()=>{o.current&&e[0]()}),t.add(()=>{o.current=!1})},style(e,t,o){let n=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:o}),this.add(()=>{Object.assign(e.style,{[t]:n})})},group(e){let t=Ge();return e(t),this.add(()=>t.dispose())},add:t=>(e.push(t),()=>{let o=e.indexOf(t);if(o>=0)for(let t of e.splice(o,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function We(){let e=function(){let e=Ge();return m(()=>e.dispose()),e}();return t=>{e.dispose(),e.nextFrame(t)}}function qe({container:e,accept:t,walk:o,enabled:n}){f(()=>{let r=e.value;if(!r||void 0!==n&&!n.value)return;let l=g(e);if(!l)return;let a=Object.assign(e=>t(e),{acceptNode:t}),i=l.createTreeWalker(r,NodeFilter.SHOW_ELEMENT,a,!1);for(;i.nextNode();)o(i.currentNode)})}var _e,Ke=((_e=Ke||{})[_e.Left=0]="Left",_e[_e.Right=2]="Right",_e);let Ye=[];!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&Ye[0]!==e.target&&(Ye.unshift(e.target),Ye=Ye.filter(e=>null!=e&&e.isConnected),Ye.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var Xe={};var Qe,Je,et=((Je=et||{})[Je.Open=0]="Open",Je[Je.Closed=1]="Closed",Je),tt=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(tt||{}),ot=((Qe=ot||{})[Qe.Pointer=0]="Pointer",Qe[Qe.Focus=1]="Focus",Qe[Qe.Other=2]="Other",Qe);let nt=Symbol("ComboboxContext");function rt(e){let t=G(nt,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,rt),t}return t}let lt=Symbol("VirtualContext"),at=k({name:"VirtualProvider",setup(e,{slots:t}){let o=rt("VirtualProvider"),n=d(()=>{let e=B(o.optionsRef);if(!e)return{start:0,end:0};let t=window.getComputedStyle(e);return{start:parseFloat(t.paddingBlockStart||t.paddingTop),end:parseFloat(t.paddingBlockEnd||t.paddingBottom)}}),r=ze(d(()=>({scrollPaddingStart:n.value.start,scrollPaddingEnd:n.value.end,count:o.virtual.value.options.length,estimateSize:()=>40,getScrollElement:()=>B(o.optionsRef),overscan:12}))),l=d(()=>{var e;return null==(e=o.virtual.value)?void 0:e.options}),a=x(0);return h([l],()=>{a.value+=1}),Y(lt,o.virtual.value?r:null),()=>[S("div",{style:{position:"relative",width:"100%",height:`${r.value.getTotalSize()}px`},ref:e=>{if(e){if("undefined"!=typeof process&&void 0!==Xe.JEST_WORKER_ID||0===o.activationTrigger.value)return;null!==o.activeOptionIndex.value&&o.virtual.value.options.length>o.activeOptionIndex.value&&r.value.scrollToIndex(o.activeOptionIndex.value)}}},r.value.getVirtualItems().map(e=>X(t.default({option:o.virtual.value.options[e.index],open:0===o.comboboxState.value})[0],{key:`${a.value}-${e.index}`,"data-index":e.index,"aria-setsize":o.virtual.value.options.length,"aria-posinset":e.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${e.start}px)`,overflowAnchor:"none"}})))]}}),it=k({name:"Combobox",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],nullable:!0,default:null},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},nullable:{type:Boolean,default:!1},multiple:{type:[Boolean],default:!1},immediate:{type:[Boolean],default:!1},virtual:{type:Object,default:null}},inheritAttrs:!1,setup(e,{slots:t,attrs:o,emit:n}){let r=x(1),l=x(null),a=x(null),s=x(null),c=x(null),v=x({static:!1,hold:!1}),p=x([]),w=x(null),m=x(2),f=x(!1);function g(e=e=>e){let t=null!==w.value?p.value[w.value]:null,o=e(p.value.slice()),n=o.length>0&&null!==o[0].dataRef.order.value?o.sort((e,t)=>e.dataRef.order.value-t.dataRef.order.value):K(o,e=>B(e.dataRef.domRef)),r=t?n.indexOf(t):null;return-1===r&&(r=null),{options:n,activeOptionIndex:r}}let k=d(()=>e.multiple?1:0),F=d(()=>e.nullable),[N,$]=b(d(()=>e.modelValue),e=>n("update:modelValue",e),d(()=>e.defaultValue)),z=d(()=>void 0===N.value?M(k.value,{1:[],0:void 0}):N.value),U=null,G=null;function W(e){return M(k.value,{0:()=>null==$?void 0:$(e),1:()=>{let t=T(_.value.value).slice(),o=T(e),n=t.findIndex(e=>_.compare(o,T(e)));return-1===n?t.push(o):t.splice(n,1),null==$?void 0:$(t)}})}let q=d(()=>{});h([q],([e],[t])=>{if(_.virtual.value&&e&&t&&null!==w.value){let o=e.indexOf(t[w.value]);w.value=-1!==o?o:null}});let _={comboboxState:r,value:z,mode:k,compare(t,o){if("string"==typeof e.by){let n=e.by;return(null==t?void 0:t[n])===(null==o?void 0:o[n])}return null===e.by?function(e,t){return e===t}(t,o):e.by(t,o)},calculateIndex:t=>_.virtual.value?null===e.by?_.virtual.value.options.indexOf(t):_.virtual.value.options.findIndex(e=>_.compare(e,t)):p.value.findIndex(e=>_.compare(e.dataRef.value,t)),defaultValue:d(()=>e.defaultValue),nullable:F,immediate:d(()=>!1),virtual:d(()=>null),inputRef:a,labelRef:l,buttonRef:s,optionsRef:c,disabled:d(()=>e.disabled),options:p,change(e){$(e)},activeOptionIndex:d(()=>{if(f.value&&null===w.value&&(_.virtual.value?_.virtual.value.options.length>0:p.value.length>0)){if(_.virtual.value){let e=_.virtual.value.options.findIndex(e=>{var t;return!(null!=(t=_.virtual.value)&&t.disabled(e))});if(-1!==e)return e}let e=p.value.findIndex(e=>!e.dataRef.disabled);if(-1!==e)return e}return w.value}),activationTrigger:m,optionsPropsRef:v,closeCombobox(){f.value=!1,!e.disabled&&1!==r.value&&(r.value=1,w.value=null)},openCombobox(){if(f.value=!0,!e.disabled&&0!==r.value){if(_.value.value){let e=_.calculateIndex(_.value.value);-1!==e&&(w.value=e)}r.value=0}},setActivationTrigger(e){m.value=e},goToOption(t,o,n){f.value=!1,null!==U&&cancelAnimationFrame(U),U=requestAnimationFrame(()=>{if(e.disabled||c.value&&!v.value.static&&1===r.value)return;if(_.virtual.value)return w.value=t===R.Specific?o:D({focus:t},{resolveItems:()=>_.virtual.value.options,resolveActiveIndex:()=>{var e,t;return null!=(t=null!=(e=_.activeOptionIndex.value)?e:_.virtual.value.options.findIndex(e=>{var t;return!(null!=(t=_.virtual.value)&&t.disabled(e))}))?t:null},resolveDisabled:e=>_.virtual.value.disabled(e),resolveId(){throw new Error("Function not implemented.")}}),void(m.value=null!=n?n:2);let l=g();if(null===l.activeOptionIndex){let e=l.options.findIndex(e=>!e.dataRef.disabled);-1!==e&&(l.activeOptionIndex=e)}let a=t===R.Specific?o:D({focus:t},{resolveItems:()=>l.options,resolveActiveIndex:()=>l.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.disabled});w.value=a,m.value=null!=n?n:2,p.value=l.options})},selectOption(e){let t=p.value.find(t=>t.id===e);if(!t)return;let{dataRef:o}=t;W(o.value)},selectActiveOption(){if(null!==_.activeOptionIndex.value){if(_.virtual.value)W(_.virtual.value.options[_.activeOptionIndex.value]);else{let{dataRef:e}=p.value[_.activeOptionIndex.value];W(e.value)}_.goToOption(R.Specific,_.activeOptionIndex.value)}},registerOption(e,t){let o=E({id:e,dataRef:t});if(_.virtual.value)return void p.value.push(o);G&&cancelAnimationFrame(G);let n=g(e=>(e.push(o),e));null===w.value&&_.isSelected(t.value.value)&&(n.activeOptionIndex=n.options.indexOf(o)),p.value=n.options,w.value=n.activeOptionIndex,m.value=2,n.options.some(e=>!B(e.dataRef.domRef))&&(G=requestAnimationFrame(()=>{let e=g();p.value=e.options,w.value=e.activeOptionIndex}))},unregisterOption(e,t){if(null!==U&&cancelAnimationFrame(U),t&&(f.value=!0),_.virtual.value)return void(p.value=p.value.filter(t=>t.id!==e));let o=g(t=>{let o=t.findIndex(t=>t.id===e);return-1!==o&&t.splice(o,1),t});p.value=o.options,w.value=o.activeOptionIndex,m.value=2},isSelected:e=>M(k.value,{0:()=>_.compare(T(_.value.value),T(e)),1:()=>T(_.value.value).some(t=>_.compare(T(t),T(e)))}),isActive:e=>w.value===_.calculateIndex(e)};C([a,s,c],()=>_.closeCombobox(),d(()=>0===r.value)),Y(nt,_),I(d(()=>M(r.value,{0:A.Open,1:A.Closed})));let X=d(()=>{var e;return null==(e=B(a))?void 0:e.closest("form")});return y(()=>{h([X],()=>{if(X.value&&void 0!==e.defaultValue)return X.value.addEventListener("reset",t),()=>{var e;null==(e=X.value)||e.removeEventListener("reset",t)};function t(){_.change(e.defaultValue)}},{immediate:!0})}),()=>{var n,l,a;let s=e,{name:d,disabled:c,form:v}=s,h=u(s,["name","disabled","form"]),p={open:0===r.value,disabled:c,activeIndex:_.activeOptionIndex.value,activeOption:null===_.activeOptionIndex.value?null:_.virtual.value?_.virtual.value.options[null!=(n=_.activeOptionIndex.value)?n:0]:null!=(a=null==(l=_.options.value[_.activeOptionIndex.value])?void 0:l.dataRef.value)?a:null,value:z.value};return S(L,[...null!=d&&null!=z.value?j({[d]:z.value}).map(([e,t])=>S(Z,H({features:P.Hidden,key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:v,disabled:c,name:e,value:t}))):[],V({theirProps:i(i({},o),O(h,["by","defaultValue","immediate","modelValue","multiple","nullable","onUpdate:modelValue","virtual"])),ourProps:{},slot:p,slots:t,attrs:o,name:"Combobox"})])}}}),st=k({name:"ComboboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:null}},setup(e,{attrs:t,slots:o}){var n;let r=null!=(n=e.id)?n:`headlessui-combobox-label-${F()}`,l=rt("ComboboxLabel");function a(){var e;null==(e=B(l.inputRef))||e.focus({preventScroll:!0})}return()=>{let n={open:0===l.comboboxState.value,disabled:l.disabled.value},i=u(e,[]),s={id:r,ref:l.labelRef,onClick:a};return V({ourProps:s,theirProps:i,slot:n,attrs:t,slots:o,name:"ComboboxLabel"})}}}),ut=k({name:"ComboboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-combobox-button-${F()}`,a=rt("ComboboxButton");function i(e){a.disabled.value||(0===a.comboboxState.value?a.closeCombobox():(e.preventDefault(),a.openCombobox()),$(()=>{var e;return null==(e=B(a.inputRef))?void 0:e.focus({preventScroll:!0})}))}function s(e){switch(e.key){case z.ArrowDown:return e.preventDefault(),e.stopPropagation(),1===a.comboboxState.value&&a.openCombobox(),void $(()=>{var e;return null==(e=a.inputRef.value)?void 0:e.focus({preventScroll:!0})});case z.ArrowUp:return e.preventDefault(),e.stopPropagation(),1===a.comboboxState.value&&(a.openCombobox(),$(()=>{a.value.value||a.goToOption(R.Last)})),void $(()=>{var e;return null==(e=a.inputRef.value)?void 0:e.focus({preventScroll:!0})});case z.Escape:if(0!==a.comboboxState.value)return;return e.preventDefault(),a.optionsRef.value&&!a.optionsPropsRef.value.static&&e.stopPropagation(),a.closeCombobox(),void $(()=>{var e;return null==(e=a.inputRef.value)?void 0:e.focus({preventScroll:!0})})}}n({el:a.buttonRef,$el:a.buttonRef});let c=N(d(()=>({as:e.as,type:t.type})),a.buttonRef);return()=>{var n,r;let d={open:0===a.comboboxState.value,disabled:a.disabled.value,value:a.value.value},v=u(e,[]),h={ref:a.buttonRef,id:l,type:c.value,tabindex:"-1","aria-haspopup":"listbox","aria-controls":null==(n=B(a.optionsRef))?void 0:n.id,"aria-expanded":0===a.comboboxState.value,"aria-labelledby":a.labelRef.value?[null==(r=B(a.labelRef))?void 0:r.id,l].join(" "):void 0,disabled:!0===a.disabled.value||void 0,onKeydown:s,onClick:i};return V({ourProps:h,theirProps:v,slot:d,attrs:t,slots:o,name:"ComboboxButton"})}}}),dt=k({name:"ComboboxInput",props:{as:{type:[Object,String],default:"input"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},displayValue:{type:Function},defaultValue:{type:String,default:void 0},id:{type:String,default:null}},emits:{change:e=>!0},setup(e,{emit:t,attrs:o,slots:n,expose:r}){var l;let a=null!=(l=e.id)?l:`headlessui-combobox-input-${F()}`,i=rt("ComboboxInput"),s=d(()=>g(B(i.inputRef))),c={value:!1};function v(){i.change(null);let e=B(i.optionsRef);e&&(e.scrollTop=0),i.goToOption(R.Nothing)}r({el:i.inputRef,$el:i.inputRef});let p=d(()=>{var t;let o=i.value.value;return B(i.inputRef)?void 0!==e.displayValue&&void 0!==o?null!=(t=e.displayValue(o))?t:"":"string"==typeof o?o:"":""});y(()=>{h([p,i.comboboxState,s],([e,t],[o,n])=>{if(c.value)return;let r=B(i.inputRef);r&&((0===n&&1===t||e!==o)&&(r.value=e),requestAnimationFrame(()=>{var e;if(c.value||!r||(null==(e=s.value)?void 0:e.activeElement)!==r)return;let{selectionStart:t,selectionEnd:o}=r;0===Math.abs((null!=o?o:0)-(null!=t?t:0))&&0===t&&r.setSelectionRange(r.value.length,r.value.length)}))},{immediate:!0}),h([i.comboboxState],([e],[t])=>{if(0===e&&1===t){if(c.value)return;let e=B(i.inputRef);if(!e)return;let t=e.value,{selectionStart:o,selectionEnd:n,selectionDirection:r}=e;e.value="",e.value=t,null!==r?e.setSelectionRange(o,n,r):e.setSelectionRange(o,n)}})});let w=x(!1);function m(){w.value=!0}function f(){Ge().nextFrame(()=>{w.value=!1})}let k=We();function b(e){switch(c.value=!0,k(()=>{c.value=!1}),e.key){case z.Enter:if(c.value=!1,0!==i.comboboxState.value||w.value)return;if(e.preventDefault(),e.stopPropagation(),null===i.activeOptionIndex.value)return void i.closeCombobox();i.selectActiveOption(),0===i.mode.value&&i.closeCombobox();break;case z.ArrowDown:return c.value=!1,e.preventDefault(),e.stopPropagation(),M(i.comboboxState.value,{0:()=>i.goToOption(R.Next),1:()=>i.openCombobox()});case z.ArrowUp:return c.value=!1,e.preventDefault(),e.stopPropagation(),M(i.comboboxState.value,{0:()=>i.goToOption(R.Previous),1:()=>{i.openCombobox(),$(()=>{i.value.value||i.goToOption(R.Last)})}});case z.Home:if(e.shiftKey)break;return c.value=!1,e.preventDefault(),e.stopPropagation(),i.goToOption(R.First);case z.PageUp:return c.value=!1,e.preventDefault(),e.stopPropagation(),i.goToOption(R.First);case z.End:if(e.shiftKey)break;return c.value=!1,e.preventDefault(),e.stopPropagation(),i.goToOption(R.Last);case z.PageDown:return c.value=!1,e.preventDefault(),e.stopPropagation(),i.goToOption(R.Last);case z.Escape:if(c.value=!1,0!==i.comboboxState.value)return;e.preventDefault(),i.optionsRef.value&&!i.optionsPropsRef.value.static&&e.stopPropagation(),i.nullable.value&&0===i.mode.value&&null===i.value.value&&v(),i.closeCombobox();break;case z.Tab:if(c.value=!1,0!==i.comboboxState.value)return;0===i.mode.value&&1!==i.activationTrigger.value&&i.selectActiveOption(),i.closeCombobox()}}function C(e){t("change",e),i.nullable.value&&0===i.mode.value&&""===e.target.value&&v(),i.openCombobox()}function I(e){var t,o,n;let r=null!=(t=e.relatedTarget)?t:Ye.find(t=>t!==e.currentTarget);if(c.value=!1,!(null!=(o=B(i.optionsRef))&&o.contains(r)||null!=(n=B(i.buttonRef))&&n.contains(r)||0!==i.comboboxState.value))return e.preventDefault(),0===i.mode.value&&(i.nullable.value&&null===i.value.value?v():1!==i.activationTrigger.value&&i.selectActiveOption()),i.closeCombobox()}function A(e){var t,o,n;let r=null!=(t=e.relatedTarget)?t:Ye.find(t=>t!==e.currentTarget);null!=(o=B(i.buttonRef))&&o.contains(r)||null!=(n=B(i.optionsRef))&&n.contains(r)||i.disabled.value||i.immediate.value&&0!==i.comboboxState.value&&(i.openCombobox(),Ge().nextFrame(()=>{i.setActivationTrigger(1)}))}let S=d(()=>{var t,o,n,r;return null!=(r=null!=(n=null!=(o=e.defaultValue)?o:void 0!==i.defaultValue.value?null==(t=e.displayValue)?void 0:t.call(e,i.defaultValue.value):null)?n:i.defaultValue.value)?r:""});return()=>{var t,r,l,s,d,c,v;let h={open:0===i.comboboxState.value},p=e,{displayValue:w,onChange:g}=p,k=u(p,["displayValue","onChange"]),x={"aria-controls":null==(t=i.optionsRef.value)?void 0:t.id,"aria-expanded":0===i.comboboxState.value,"aria-activedescendant":null===i.activeOptionIndex.value?void 0:i.virtual.value?null==(r=i.options.value.find(e=>!i.virtual.value.disabled(e.dataRef.value)&&i.compare(e.dataRef.value,i.virtual.value.options[i.activeOptionIndex.value])))?void 0:r.id:null==(l=i.options.value[i.activeOptionIndex.value])?void 0:l.id,"aria-labelledby":null!=(c=null==(s=B(i.labelRef))?void 0:s.id)?c:null==(d=B(i.buttonRef))?void 0:d.id,"aria-autocomplete":"list",id:a,onCompositionstart:m,onCompositionend:f,onKeydown:b,onInput:C,onFocus:A,onBlur:I,role:"combobox",type:null!=(v=o.type)?v:"text",tabIndex:0,ref:i.inputRef,defaultValue:S.value,disabled:!0===i.disabled.value||void 0};return V({ourProps:x,theirProps:k,slot:h,attrs:o,slots:n,features:U.RenderStrategy|U.Static,name:"ComboboxInput"})}}}),ct=k({name:"ComboboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},hold:{type:[Boolean],default:!1}},setup(e,{attrs:t,slots:o,expose:n}){let r=rt("ComboboxOptions"),l=`headlessui-combobox-options-${F()}`;n({el:r.optionsRef,$el:r.optionsRef}),f(()=>{r.optionsPropsRef.value.static=e.static}),f(()=>{r.optionsPropsRef.value.hold=e.hold});let a=_(),u=d(()=>null!==a?(a.value&A.Open)===A.Open:0===r.comboboxState.value);function c(e){e.preventDefault()}return qe({container:d(()=>B(r.optionsRef)),enabled:d(()=>0===r.comboboxState.value),accept:e=>"option"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}}),()=>{var n,a,d;let v={open:0===r.comboboxState.value},h={"aria-labelledby":null!=(d=null==(n=B(r.labelRef))?void 0:n.id)?d:null==(a=B(r.buttonRef))?void 0:a.id,id:l,ref:r.optionsRef,role:"listbox","aria-multiselectable":1===r.mode.value||void 0,onMousedown:c},p=O(e,["hold"]);return V({ourProps:h,theirProps:p,slot:v,attrs:t,slots:r.virtual.value&&0===r.comboboxState.value?s(i({},o),{default:()=>[S(at,{},o.default)]}):o,features:U.RenderStrategy|U.Static,visible:u.value,name:"ComboboxOptions"})}}}),vt=k({name:"ComboboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},order:{type:[Number],default:null}},setup(e,{slots:t,attrs:o,expose:n}){let r=rt("ComboboxOption"),l=`headlessui-combobox-option-${F()}`,a=x(null),i=d(()=>e.disabled);n({el:a,$el:a});let s=d(()=>{var t;return r.virtual.value?r.activeOptionIndex.value===r.calculateIndex(e.value):null!==r.activeOptionIndex.value&&(null==(t=r.options.value[r.activeOptionIndex.value])?void 0:t.id)===l}),u=d(()=>r.isSelected(e.value)),c=G(lt,null),v=d(()=>({disabled:e.disabled,value:e.value,domRef:a,order:d(()=>e.order)}));function h(e){e.preventDefault(),e.button===Ke.Left&&(i.value||(r.selectOption(l),q()||requestAnimationFrame(()=>{var e;return null==(e=B(r.inputRef))?void 0:e.focus({preventScroll:!0})}),0===r.mode.value&&r.closeCombobox()))}function p(){var t;if(e.disabled||null!=(t=r.virtual.value)&&t.disabled(e.value))return r.goToOption(R.Nothing);let o=r.calculateIndex(e.value);r.goToOption(R.Specific,o)}y(()=>r.registerOption(l,v)),m(()=>r.unregisterOption(l,s.value)),f(()=>{let e=B(a);e&&(null==c||c.value.measureElement(e))}),f(()=>{0===r.comboboxState.value&&s.value&&(r.virtual.value||0!==r.activationTrigger.value&&$(()=>{var e,t;return null==(t=null==(e=B(a))?void 0:e.scrollIntoView)?void 0:t.call(e,{block:"nearest"})}))});let w=W();function g(e){w.update(e)}function k(t){var o;if(!w.wasMoved(t)||e.disabled||null!=(o=r.virtual.value)&&o.disabled(e.value)||s.value)return;let n=r.calculateIndex(e.value);r.goToOption(R.Specific,n,0)}function b(t){var o;w.wasMoved(t)&&(e.disabled||null!=(o=r.virtual.value)&&o.disabled(e.value)||s.value&&(r.optionsPropsRef.value.hold||r.goToOption(R.Nothing)))}return()=>{let{disabled:n}=e,r={active:s.value,selected:u.value,disabled:n},i={id:l,ref:a,role:"option",tabIndex:!0===n?void 0:-1,"aria-disabled":!0===n||void 0,"aria-selected":u.value,disabled:void 0,onMousedown:h,onFocus:p,onPointerenter:g,onMouseenter:g,onPointermove:k,onMousemove:k,onPointerleave:b,onMouseleave:b},d=O(e,["order","value"]);return V({ourProps:i,theirProps:d,slot:r,attrs:o,slots:t,name:"ComboboxOption"})}}});function ht(e,t,o,n){Q.isServer||f(r=>{(e=null!=e?e:window).addEventListener(t,o,n),r(()=>e.removeEventListener(t,o,n))})}var pt=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(pt||{});function wt(){let e=x(0);return J("keydown",t=>{"Tab"===t.key&&(e.value=t.shiftKey?1:0)}),e}function mt(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let o of e.value){let e=B(o);e instanceof HTMLElement&&t.add(e)}return t}var ft,gt=((ft=gt||{})[ft.None=1]="None",ft[ft.InitialFocus=2]="InitialFocus",ft[ft.TabLock=4]="TabLock",ft[ft.FocusLock=8]="FocusLock",ft[ft.RestoreFocus=16]="RestoreFocus",ft[ft.All=30]="All",ft);let kt=Object.assign(k({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:x(new Set)}},inheritAttrs:!1,setup(e,{attrs:t,slots:o,expose:n}){let r=x(null);n({el:r,$el:r});let l=d(()=>g(r)),a=x(!1);y(()=>a.value=!0),m(()=>a.value=!1),function({ownerDocument:e},t){let o=function(e){let t=x(Ye.slice());return h([e],([e],[o])=>{!0===o&&!1===e?Ue(()=>{t.value.splice(0)}):!1===o&&!0===e&&(t.value=Ye.slice())},{flush:"post"}),()=>{var e;return null!=(e=t.value.find(e=>null!=e&&e.isConnected))?e:null}}(t);y(()=>{f(()=>{var n,r;t.value||(null==(n=e.value)?void 0:n.activeElement)===(null==(r=e.value)?void 0:r.body)&&oe(o())},{flush:"post"})}),m(()=>{t.value&&oe(o())})}({ownerDocument:l},d(()=>a.value&&Boolean(16&e.features)));let s=function({ownerDocument:e,container:t,initialFocus:o},n){let r=x(null),l=x(!1);return y(()=>l.value=!0),m(()=>l.value=!1),y(()=>{h([t,o,n],(a,i)=>{if(a.every((e,t)=>(null==i?void 0:i[t])===e)||!n.value)return;let s=B(t);s&&Ue(()=>{var t,n;if(!l.value)return;let a=B(o),i=null==(t=e.value)?void 0:t.activeElement;if(a){if(a===i)return void(r.value=i)}else if(s.contains(i))return void(r.value=i);a?oe(a):ee(s,te.First|te.NoScroll)===ne.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),r.value=null==(n=e.value)?void 0:n.activeElement})},{immediate:!0,flush:"post"})}),r}({ownerDocument:l,container:r,initialFocus:d(()=>e.initialFocus)},d(()=>a.value&&Boolean(2&e.features)));!function({ownerDocument:e,container:t,containers:o,previousActiveElement:n},r){var l;ht(null==(l=e.value)?void 0:l.defaultView,"focus",e=>{if(!r.value)return;let l=mt(o);B(t)instanceof HTMLElement&&l.add(B(t));let a=n.value;if(!a)return;let i=e.target;i&&i instanceof HTMLElement?xt(l,i)?(n.value=i,oe(i)):(e.preventDefault(),e.stopPropagation(),oe(a)):oe(n.value)},!0)}({ownerDocument:l,container:r,containers:e.containers,previousActiveElement:s},d(()=>a.value&&Boolean(8&e.features)));let c=wt();function v(e){let t=B(r);t&&M(c.value,{[pt.Forwards]:()=>{ee(t,te.First,{skipElements:[e.relatedTarget]})},[pt.Backwards]:()=>{ee(t,te.Last,{skipElements:[e.relatedTarget]})}})}let p=x(!1);function w(e){"Tab"===e.key&&(p.value=!0,requestAnimationFrame(()=>{p.value=!1}))}function k(t){if(!a.value)return;let o=mt(e.containers);B(r)instanceof HTMLElement&&o.add(B(r));let n=t.relatedTarget;n instanceof HTMLElement&&"true"!==n.dataset.headlessuiFocusGuard&&(xt(o,n)||(p.value?ee(B(r),M(c.value,{[pt.Forwards]:()=>te.Next,[pt.Backwards]:()=>te.Previous})|te.WrapAround,{relativeTo:t.target}):t.target instanceof HTMLElement&&oe(t.target)))}return()=>{let n={ref:r,onKeydown:w,onFocusout:k},l=e,{features:a,initialFocus:s,containers:d}=l,c=u(l,["features","initialFocus","containers"]);return S(L,[Boolean(4&a)&&S(Z,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:v,features:P.Focusable}),V({ourProps:n,theirProps:i(i({},t),c),slot:{},attrs:t,slots:o,name:"FocusTrap"}),Boolean(4&a)&&S(Z,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:v,features:P.Focusable})])}}}),{features:gt});function xt(e,t){for(let o of e)if(o.contains(t))return!0;return!1}function bt(){let e;return{before({doc:t}){var o;let n=t.documentElement;e=(null!=(o=t.defaultView)?o:window).innerWidth-n.clientWidth},after({doc:t,d:o}){let n=t.documentElement,r=n.clientWidth-n.offsetWidth,l=e-r;o.style(n,"paddingRight",`${l}px`)}}}function Mt(e){let t={};for(let o of e)Object.assign(t,o(t));return t}let Ct=function(e,t){let o=e(),n=new Set;return{getSnapshot:()=>o,subscribe:e=>(n.add(e),()=>n.delete(e)),dispatch(e,...r){let l=t[e].call(o,...r);l&&(o=l,n.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var o;let n=null!=(o=this.get(e))?o:{doc:e,count:0,d:Ge(),meta:new Set};return n.count++,n.meta.add(t),this.set(e,n),this},POP(e,t){let o=this.get(e);return o&&(o.count--,o.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:o}){let n={doc:e,d:t,meta:Mt(o)},r=[re()?{before({doc:t,d:o,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}o.microTask(()=>{var n;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=Ge();e.style(t.documentElement,"scrollBehavior","auto"),o.add(()=>o.microTask(()=>e.dispose()))}let l=null!=(n=window.scrollY)?n:window.pageYOffset,a=null;o.addEventListener(t,"click",o=>{if(o.target instanceof HTMLElement)try{let e=o.target.closest("a");if(!e)return;let{hash:n}=new URL(e.href),l=t.querySelector(n);l&&!r(l)&&(a=l)}catch(e){}},!0),o.addEventListener(t,"touchstart",e=>{if(e.target instanceof HTMLElement)if(r(e.target)){let t=e.target;for(;t.parentElement&&r(t.parentElement);)t=t.parentElement;o.style(t,"overscrollBehavior","contain")}else o.style(e.target,"touchAction","none")}),o.addEventListener(t,"touchmove",e=>{if(e.target instanceof HTMLElement){if("INPUT"===e.target.tagName)return;if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),o.add(()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;l!==t&&window.scrollTo(0,l),a&&a.isConnected&&(a.scrollIntoView({block:"nearest"}),a=null)})})}}:{},bt(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];r.forEach(({before:e})=>null==e?void 0:e(n)),r.forEach(({after:e})=>null==e?void 0:e(n))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});function It(e,t,o){let n=function(e){let t=v(e.getSnapshot());return m(e.subscribe(()=>{t.value=e.getSnapshot()})),t}(Ct),r=d(()=>{let t=e.value?n.value.get(e.value):void 0;return!!t&&t.count>0});return h([e,t],([e,t],[n],r)=>{if(!e||!t)return;Ct.dispatch("PUSH",e,o);let l=!1;r(()=>{l||(Ct.dispatch("POP",null!=n?n:e,o),l=!0)})},{immediate:!0}),r}Ct.subscribe(()=>{let e=Ct.getSnapshot(),t=new Map;for(let[o]of e)t.set(o,o.documentElement.style.overflow);for(let o of e.values()){let e="hidden"===t.get(o.doc),n=0!==o.count;(n&&!e||!n&&e)&&Ct.dispatch(o.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",o),0===o.count&&Ct.dispatch("TEARDOWN",o)}});let At=new Map,Bt=new Map;function yt(e,t=x(!0)){f(o=>{var n;if(!t.value)return;let r=B(e);if(!r)return;o(function(){var e;if(!r)return;let t=null!=(e=Bt.get(r))?e:1;if(1===t?Bt.delete(r):Bt.set(r,t-1),1!==t)return;let o=At.get(r);o&&(null===o["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",o["aria-hidden"]),r.inert=o.inert,At.delete(r))});let l=null!=(n=Bt.get(r))?n:0;Bt.set(r,l+1),0===l&&(At.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0)})}function St({defaultContainers:e=[],portals:t,mainTreeNodeRef:o}={}){let n=x(null),r=g(n);function l(){var o,l,a;let i=[];for(let t of e)null!==t&&(t instanceof HTMLElement?i.push(t):"value"in t&&t.value instanceof HTMLElement&&i.push(t.value));if(null!=t&&t.value)for(let e of t.value)i.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(B(n))||e.contains(null==(a=null==(l=B(n))?void 0:l.getRootNode())?void 0:a.host)||i.some(t=>e.contains(t))||i.push(e));return i}return{resolveContainers:l,contains:e=>l().some(t=>t.contains(e)),mainTreeNodeRef:n,MainTreeNode:()=>null!=o?null:S(Z,{features:P.Hidden,ref:n})}}let jt=Symbol("ForcePortalRootContext");let Vt=k({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup:(e,{slots:t,attrs:o})=>(Y(jt,e.force),()=>{let n=e,{force:r}=n,l=u(n,["force"]);return V({theirProps:l,ourProps:{},slot:{},slots:t,attrs:o,name:"ForcePortalRoot"})})}),Zt=Symbol("StackContext");var Ht=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(Ht||{});function Pt({type:e,enabled:t,element:o,onUpdate:n}){let r=G(Zt,()=>{});function l(...e){null==n||n(...e),r(...e)}y(()=>{h(t,(t,n)=>{t?l(0,e,o):!0===n&&l(1,e,o)},{immediate:!0,flush:"sync"})}),m(()=>{t.value&&l(1,e,o)}),Y(Zt,l)}let Ot=Symbol("DescriptionContext");function Lt({slot:e=x({}),name:t="Description",props:o={}}={}){let n=x([]);return Y(Ot,{register:function(e){return n.value.push(e),()=>{let t=n.value.indexOf(e);-1!==t&&n.value.splice(t,1)}},slot:e,name:t,props:o}),d(()=>n.value.length>0?n.value.join(" "):void 0)}let Tt=k({name:"Description",props:{as:{type:[Object,String],default:"p"},id:{type:String,default:null}},setup(e,{attrs:t,slots:o}){var n;let r=null!=(n=e.id)?n:`headlessui-description-${F()}`,l=function(){let e=G(Ot,null);if(null===e)throw new Error("Missing parent");return e}();return y(()=>m(l.register(r))),()=>{let{name:n="Description",slot:a=x({}),props:d={}}=l,v=u(e,[]),h=s(i({},Object.entries(d).reduce((e,[t,o])=>Object.assign(e,{[t]:c(o)}),{})),{id:r});return V({ourProps:h,theirProps:v,slot:a.value,attrs:t,slots:o,name:n})}}});const Et=new WeakMap;function Rt(e,t){let o=t(function(e){var t;return null!=(t=Et.get(e))?t:0}(e));return o<=0?Et.delete(e):Et.set(e,o),o}let Dt=k({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:o}){let n=x(null),r=d(()=>g(n)),l=G(jt,!1),a=G($t,null),i=x(!0===l||null==a?function(e){let t=g(e);if(!t){if(null===e)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let o=t.getElementById("headlessui-portal-root");if(o)return o;let n=t.createElement("div");return n.setAttribute("id","headlessui-portal-root"),t.body.appendChild(n)}(n.value):a.resolveTarget());i.value&&Rt(i.value,e=>e+1);let s=x(!1);y(()=>{s.value=!0}),f(()=>{l||null!=a&&(i.value=a.resolveTarget())});let u=G(Ft,null),c=!1,v=ae();return h(n,()=>{if(c||!u)return;let e=B(n);e&&(m(u.register(e),v),c=!0)}),m(()=>{var e,t;let o=null==(e=r.value)?void 0:e.getElementById("headlessui-portal-root");!o||i.value!==o||Rt(i.value,e=>e-1)||i.value.children.length>0||null==(t=i.value.parentElement)||t.removeChild(i.value)}),()=>{if(!s.value||null===i.value)return null;let r={ref:n,"data-headlessui-portal":""};return S(le,{to:i.value},V({ourProps:r,theirProps:e,slot:{},attrs:o,slots:t,name:"Portal"}))}}}),Ft=Symbol("PortalParentContext");function Nt(){let e=G(Ft,null),t=x([]);function o(o){let n=t.value.indexOf(o);-1!==n&&t.value.splice(n,1),e&&e.unregister(o)}let n={register:function(n){return t.value.push(n),e&&e.register(n),()=>o(n)},unregister:o,portals:t};return[t,k({name:"PortalWrapper",setup:(e,{slots:t})=>(Y(Ft,n),()=>{var e;return null==(e=t.default)?void 0:e.call(t)})})]}let $t=Symbol("PortalGroupContext"),zt=k({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:o}){let n=E({resolveTarget:()=>e.target});return Y($t,n),()=>{let n=e,{target:r}=n,l=u(n,["target"]);return V({theirProps:l,ourProps:{},slot:{},attrs:t,slots:o,name:"PortalGroup"})}}});var Ut,Gt=((Ut=Gt||{})[Ut.Open=0]="Open",Ut[Ut.Closed=1]="Closed",Ut);let Wt=Symbol("DialogContext");function qt(e){let t=G(Wt,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,qt),t}return t}let _t="DC8F892D-2EBD-447C-A4C8-A03058436FF4",Kt=k({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:_t},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:"dialog"}},emits:{close:e=>!0},setup(e,{emit:t,attrs:o,slots:n,expose:r}){var l,a;let c=null!=(l=e.id)?l:`headlessui-dialog-${F()}`,v=x(!1);y(()=>{v.value=!0});let h=!1,p=d(()=>"dialog"===e.role||"alertdialog"===e.role?e.role:(h||(h=!0,console.warn(`Invalid role [${p}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")),w=x(0),m=_(),k=d(()=>e.open===_t&&null!==m?(m.value&A.Open)===A.Open:e.open),b=x(null),I=d(()=>g(b));if(r({el:b,$el:b}),e.open===_t&&null===m)throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if("boolean"!=typeof k.value)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${k.value===_t?void 0:e.open}`);let j=d(()=>v.value&&k.value?0:1),Z=d(()=>0===j.value),H=d(()=>w.value>1),P=null!==G(Wt,null),[O,L]=Nt(),{resolveContainers:T,mainTreeNodeRef:E,MainTreeNode:R}=St({portals:O,defaultContainers:[d(()=>{var e;return null!=(e=ee.panelRef.value)?e:b.value})]}),D=d(()=>H.value?"parent":"leaf"),N=d(()=>null!==m&&(m.value&A.Closing)===A.Closing),W=d(()=>!P&&!N.value&&Z.value),q=d(()=>{var e,t,o;return null!=(o=Array.from(null!=(t=null==(e=I.value)?void 0:e.querySelectorAll("body > *"))?t:[]).find(e=>"headlessui-portal-root"!==e.id&&(e.contains(B(E))&&e instanceof HTMLElement)))?o:null});yt(q,W);let K=d(()=>!!H.value||Z.value),X=d(()=>{var e,t,o;return null!=(o=Array.from(null!=(t=null==(e=I.value)?void 0:e.querySelectorAll("[data-headlessui-portal]"))?t:[]).find(e=>e.contains(B(E))&&e instanceof HTMLElement))?o:null});yt(X,K),Pt({type:"Dialog",enabled:d(()=>0===j.value),element:b,onUpdate:(e,t)=>{if("Dialog"===t)return M(e,{[Ht.Add]:()=>w.value+=1,[Ht.Remove]:()=>w.value-=1})}});let Q=Lt({name:"DialogDescription",slot:d(()=>({open:k.value}))}),J=x(null),ee={titleId:J,panelRef:x(null),dialogState:j,setTitleId(e){J.value!==e&&(J.value=e)},close(){t("close",!1)}};Y(Wt,ee);let te=d(()=>!(!Z.value||H.value));C(T,(e,t)=>{e.preventDefault(),ee.close(),$(()=>null==t?void 0:t.focus())},te);let oe=d(()=>!(H.value||0!==j.value));ht(null==(a=I.value)?void 0:a.defaultView,"keydown",e=>{oe.value&&(e.defaultPrevented||e.key===z.Escape&&(e.preventDefault(),e.stopPropagation(),ee.close()))});let ne=d(()=>!(N.value||0!==j.value||P));return It(I,ne,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],T]}}),f(e=>{if(0!==j.value)return;let t=B(b);if(!t)return;let o=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&ee.close()}});o.observe(t),e(()=>o.disconnect())}),()=>{let t=e,{open:r,initialFocus:l}=t,a=u(t,["open","initialFocus"]),d=s(i({},o),{ref:b,id:c,role:p.value,"aria-modal":0===j.value||void 0,"aria-labelledby":J.value,"aria-describedby":Q.value}),v={open:0===j.value};return S(Vt,{force:!0},()=>[S(Dt,()=>S(zt,{target:b.value},()=>S(Vt,{force:!1},()=>S(kt,{initialFocus:l,containers:T,features:Z.value?M(D.value,{parent:kt.features.RestoreFocus,leaf:kt.features.All&~kt.features.FocusLock}):kt.features.None},()=>S(L,{},()=>V({ourProps:d,theirProps:i(i({},a),o),slot:v,attrs:o,slots:n,visible:0===j.value,features:U.RenderStrategy|U.Static,name:"Dialog"})))))),S(R)])}}}),Yt=k({name:"DialogOverlay",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:o}){var n;let r=null!=(n=e.id)?n:`headlessui-dialog-overlay-${F()}`,l=qt("DialogOverlay");function a(e){e.target===e.currentTarget&&(e.preventDefault(),e.stopPropagation(),l.close())}return()=>{let n=u(e,[]);return V({ourProps:{id:r,"aria-hidden":!0,onClick:a},theirProps:n,slot:{open:0===l.dialogState.value},attrs:t,slots:o,name:"DialogOverlay"})}}}),Xt=k({name:"DialogBackdrop",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-dialog-backdrop-${F()}`,a=qt("DialogBackdrop"),s=x(null);return n({el:s,$el:s}),y(()=>{if(null===a.panelRef.value)throw new Error("A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.")}),()=>{let n=u(e,[]),r={id:l,ref:s,"aria-hidden":!0};return S(Vt,{force:!0},()=>S(Dt,()=>V({ourProps:r,theirProps:i(i({},t),n),slot:{open:0===a.dialogState.value},attrs:t,slots:o,name:"DialogBackdrop"})))}}}),Qt=k({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-dialog-panel-${F()}`,a=qt("DialogPanel");function i(e){e.stopPropagation()}return n({el:a.panelRef,$el:a.panelRef}),()=>{let n=u(e,[]),r={id:l,ref:a.panelRef,onClick:i};return V({ourProps:r,theirProps:n,slot:{open:0===a.dialogState.value},attrs:t,slots:o,name:"DialogPanel"})}}}),Jt=k({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:null}},setup(e,{attrs:t,slots:o}){var n;let r=null!=(n=e.id)?n:`headlessui-dialog-title-${F()}`,l=qt("DialogTitle");return y(()=>{l.setTitleId(r),m(()=>l.setTitleId(null))}),()=>{let n=u(e,[]);return V({ourProps:{id:r},theirProps:n,slot:{open:0===l.dialogState.value},attrs:t,slots:o,name:"DialogTitle"})}}}),eo=Tt;var to,oo=((to=oo||{})[to.Open=0]="Open",to[to.Closed=1]="Closed",to);let no=Symbol("DisclosureContext");function ro(e){let t=G(no,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ro),t}return t}let lo=Symbol("DisclosurePanelContext");let ao=k({name:"Disclosure",props:{as:{type:[Object,String],default:"template"},defaultOpen:{type:[Boolean],default:!1}},setup(e,{slots:t,attrs:o}){let n=x(e.defaultOpen?0:1),r=x(null),l=x(null),a={buttonId:x(`headlessui-disclosure-button-${F()}`),panelId:x(`headlessui-disclosure-panel-${F()}`),disclosureState:n,panel:r,button:l,toggleDisclosure(){n.value=M(n.value,{0:1,1:0})},closeDisclosure(){1!==n.value&&(n.value=1)},close(e){a.closeDisclosure();let t=e?e instanceof HTMLElement?e:e.value instanceof HTMLElement?B(e):B(a.button):B(a.button);null==t||t.focus()}};return Y(no,a),I(d(()=>M(n.value,{0:A.Open,1:A.Closed}))),()=>{let r=e,{defaultOpen:l}=r,i=u(r,["defaultOpen"]),s={open:0===n.value,close:a.close};return V({theirProps:i,ourProps:{},slot:s,slots:t,attrs:o,name:"Disclosure"})}}}),io=k({name:"DisclosureButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:o,expose:n}){let r=ro("DisclosureButton"),l=G(lo,null),a=d(()=>null!==l&&l.value===r.panelId.value);y(()=>{a.value||null!==e.id&&(r.buttonId.value=e.id)}),m(()=>{a.value||(r.buttonId.value=null)});let i=x(null);n({el:i,$el:i}),a.value||f(()=>{r.button.value=i.value});let s=N(d(()=>({as:e.as,type:t.type})),i);function c(){var t;e.disabled||(a.value?(r.toggleDisclosure(),null==(t=B(r.button))||t.focus()):r.toggleDisclosure())}function v(t){var o;if(!e.disabled)if(a.value)switch(t.key){case z.Space:case z.Enter:t.preventDefault(),t.stopPropagation(),r.toggleDisclosure(),null==(o=B(r.button))||o.focus()}else switch(t.key){case z.Space:case z.Enter:t.preventDefault(),t.stopPropagation(),r.toggleDisclosure()}}function h(e){if(e.key===z.Space)e.preventDefault()}return()=>{var n;let l={open:0===r.disclosureState.value},d=e,{id:p}=d,w=u(d,["id"]),m=a.value?{ref:i,type:s.value,onClick:c,onKeydown:v}:{id:null!=(n=r.buttonId.value)?n:p,ref:i,type:s.value,"aria-expanded":0===r.disclosureState.value,"aria-controls":0===r.disclosureState.value||B(r.panel)?r.panelId.value:void 0,disabled:!!e.disabled||void 0,onClick:c,onKeydown:v,onKeyup:h};return V({ourProps:m,theirProps:w,slot:l,attrs:t,slots:o,name:"DisclosureButton"})}}}),so=k({name:"DisclosurePanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:t,slots:o,expose:n}){let r=ro("DisclosurePanel");y(()=>{null!==e.id&&(r.panelId.value=e.id)}),m(()=>{r.panelId.value=null}),n({el:r.panel,$el:r.panel}),Y(lo,r.panelId);let l=_(),a=d(()=>null!==l?(l.value&A.Open)===A.Open:0===r.disclosureState.value);return()=>{var n;let l={open:0===r.disclosureState.value,close:r.close},i=e,{id:s}=i,d=u(i,["id"]),c={id:null!=(n=r.panelId.value)?n:s,ref:r.panel};return V({ourProps:c,theirProps:d,slot:l,attrs:t,slots:o,features:U.RenderStrategy|U.Static,visible:a.value,name:"DisclosurePanel"})}}});var uo,co=((uo=co||{})[uo.Open=0]="Open",uo[uo.Closed=1]="Closed",uo),vo=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(vo||{});let ho=Symbol("MenuContext");function po(e){let t=G(ho,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,po),t}return t}let wo=k({name:"Menu",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:t,attrs:o}){let n=x(1),r=x(null),l=x(null),a=x([]),i=x(""),s=x(null),u=x(1);function c(e=e=>e){let t=null!==s.value?a.value[s.value]:null,o=K(e(a.value.slice()),e=>B(e.dataRef.domRef)),n=t?o.indexOf(t):null;return-1===n&&(n=null),{items:o,activeItemIndex:n}}let v={menuState:n,buttonRef:r,itemsRef:l,items:a,searchQuery:i,activeItemIndex:s,activationTrigger:u,closeMenu:()=>{n.value=1,s.value=null},openMenu:()=>n.value=0,goToItem(e,t,o){let n=c(),r=D(e===R.Specific?{focus:R.Specific,id:t}:{focus:e},{resolveItems:()=>n.items,resolveActiveIndex:()=>n.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.disabled});i.value="",s.value=r,u.value=null!=o?o:1,a.value=n.items},search(e){let t=""!==i.value?0:1;i.value+=e.toLowerCase();let o=(null!==s.value?a.value.slice(s.value+t).concat(a.value.slice(0,s.value+t)):a.value).find(e=>e.dataRef.textValue.startsWith(i.value)&&!e.dataRef.disabled),n=o?a.value.indexOf(o):-1;-1===n||n===s.value||(s.value=n,u.value=1)},clearSearch(){i.value=""},registerItem(e,t){let o=c(o=>[...o,{id:e,dataRef:t}]);a.value=o.items,s.value=o.activeItemIndex,u.value=1},unregisterItem(e){let t=c(t=>{let o=t.findIndex(t=>t.id===e);return-1!==o&&t.splice(o,1),t});a.value=t.items,s.value=t.activeItemIndex,u.value=1}};return C([r,l],(e,t)=>{var o;v.closeMenu(),ie(t,se.Loose)||(e.preventDefault(),null==(o=B(r))||o.focus())},d(()=>0===n.value)),Y(ho,v),I(d(()=>M(n.value,{0:A.Open,1:A.Closed}))),()=>{let r={open:0===n.value,close:v.closeMenu};return V({ourProps:{},theirProps:e,slot:r,slots:t,attrs:o,name:"Menu"})}}}),mo=k({name:"MenuButton",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-menu-button-${F()}`,a=po("MenuButton");function i(e){switch(e.key){case z.Space:case z.Enter:case z.ArrowDown:e.preventDefault(),e.stopPropagation(),a.openMenu(),$(()=>{var e;null==(e=B(a.itemsRef))||e.focus({preventScroll:!0}),a.goToItem(R.First)});break;case z.ArrowUp:e.preventDefault(),e.stopPropagation(),a.openMenu(),$(()=>{var e;null==(e=B(a.itemsRef))||e.focus({preventScroll:!0}),a.goToItem(R.Last)})}}function s(e){if(e.key===z.Space)e.preventDefault()}function c(t){e.disabled||(0===a.menuState.value?(a.closeMenu(),$(()=>{var e;return null==(e=B(a.buttonRef))?void 0:e.focus({preventScroll:!0})})):(t.preventDefault(),a.openMenu(),function(e){requestAnimationFrame(()=>requestAnimationFrame(e))}(()=>{var e;return null==(e=B(a.itemsRef))?void 0:e.focus({preventScroll:!0})})))}n({el:a.buttonRef,$el:a.buttonRef});let v=N(d(()=>({as:e.as,type:t.type})),a.buttonRef);return()=>{var n;let r={open:0===a.menuState.value},d=u(e,[]),h={ref:a.buttonRef,id:l,type:v.value,"aria-haspopup":"menu","aria-controls":null==(n=B(a.itemsRef))?void 0:n.id,"aria-expanded":0===a.menuState.value,onKeydown:i,onKeyup:s,onClick:c};return V({ourProps:h,theirProps:d,slot:r,attrs:t,slots:o,name:"MenuButton"})}}}),fo=k({name:"MenuItems",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-menu-items-${F()}`,a=po("MenuItems"),i=x(null);function s(e){var t;switch(i.value&&clearTimeout(i.value),e.key){case z.Space:if(""!==a.searchQuery.value)return e.preventDefault(),e.stopPropagation(),a.search(e.key);case z.Enter:if(e.preventDefault(),e.stopPropagation(),null!==a.activeItemIndex.value){let e=a.items.value[a.activeItemIndex.value];null==(t=B(e.dataRef.domRef))||t.click()}a.closeMenu(),de(B(a.buttonRef));break;case z.ArrowDown:return e.preventDefault(),e.stopPropagation(),a.goToItem(R.Next);case z.ArrowUp:return e.preventDefault(),e.stopPropagation(),a.goToItem(R.Previous);case z.Home:case z.PageUp:return e.preventDefault(),e.stopPropagation(),a.goToItem(R.First);case z.End:case z.PageDown:return e.preventDefault(),e.stopPropagation(),a.goToItem(R.Last);case z.Escape:e.preventDefault(),e.stopPropagation(),a.closeMenu(),$(()=>{var e;return null==(e=B(a.buttonRef))?void 0:e.focus({preventScroll:!0})});break;case z.Tab:e.preventDefault(),e.stopPropagation(),a.closeMenu(),$(()=>ce(B(a.buttonRef),e.shiftKey?te.Previous:te.Next));break;default:1===e.key.length&&(a.search(e.key),i.value=setTimeout(()=>a.clearSearch(),350))}}function c(e){if(e.key===z.Space)e.preventDefault()}n({el:a.itemsRef,$el:a.itemsRef}),qe({container:d(()=>B(a.itemsRef)),enabled:d(()=>0===a.menuState.value),accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let v=_(),h=d(()=>null!==v?(v.value&A.Open)===A.Open:0===a.menuState.value);return()=>{var n,r;let i={open:0===a.menuState.value},d=u(e,[]),v={"aria-activedescendant":null===a.activeItemIndex.value||null==(n=a.items.value[a.activeItemIndex.value])?void 0:n.id,"aria-labelledby":null==(r=B(a.buttonRef))?void 0:r.id,id:l,onKeydown:s,onKeyup:c,role:"menu",tabIndex:0,ref:a.itemsRef};return V({ourProps:v,theirProps:d,slot:i,attrs:t,slots:o,features:U.RenderStrategy|U.Static,visible:h.value,name:"MenuItems"})}}}),go=k({name:"MenuItem",inheritAttrs:!1,props:{as:{type:[Object,String],default:"template"},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-menu-item-${F()}`,a=po("MenuItem"),s=x(null);n({el:s,$el:s});let c=d(()=>null!==a.activeItemIndex.value&&a.items.value[a.activeItemIndex.value].id===l),v=ue(s),h=d(()=>({disabled:e.disabled,get textValue(){return v()},domRef:s}));function p(t){if(e.disabled)return t.preventDefault();a.closeMenu(),de(B(a.buttonRef))}function w(){if(e.disabled)return a.goToItem(R.Nothing);a.goToItem(R.Specific,l)}y(()=>a.registerItem(l,h)),m(()=>a.unregisterItem(l)),f(()=>{0===a.menuState.value&&c.value&&0!==a.activationTrigger.value&&$(()=>{var e,t;return null==(t=null==(e=B(s))?void 0:e.scrollIntoView)?void 0:t.call(e,{block:"nearest"})})});let g=W();function k(e){g.update(e)}function b(t){g.wasMoved(t)&&(e.disabled||c.value||a.goToItem(R.Specific,l,0))}function M(t){g.wasMoved(t)&&(e.disabled||c.value&&a.goToItem(R.Nothing))}return()=>{let n=e,{disabled:r}=n,d=u(n,["disabled"]),v={active:c.value,disabled:r,close:a.closeMenu};return V({ourProps:{id:l,ref:s,role:"menuitem",tabIndex:!0===r?void 0:-1,"aria-disabled":!0===r||void 0,onClick:p,onFocus:w,onPointerenter:k,onMouseenter:k,onPointermove:b,onMousemove:b,onPointerleave:M,onMouseleave:M},theirProps:i(i({},o),d),slot:v,attrs:o,slots:t,name:"MenuItem"})}}});var ko,xo=((ko=xo||{})[ko.Open=0]="Open",ko[ko.Closed=1]="Closed",ko);let bo=Symbol("PopoverContext");function Mo(e){let t=G(bo,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <${Bo.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Mo),t}return t}let Co=Symbol("PopoverGroupContext");function Io(){return G(Co,null)}let Ao=Symbol("PopoverPanelContext");let Bo=k({name:"Popover",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:o,expose:n}){var r;let l=x(null);n({el:l,$el:l});let a=x(1),s=x(null),u=x(null),c=x(null),v=x(null),h=d(()=>g(l)),p=d(()=>{var e,t;if(!B(s)||!B(v))return!1;for(let u of document.querySelectorAll("body > *"))if(Number(null==u?void 0:u.contains(B(s)))^Number(null==u?void 0:u.contains(B(v))))return!0;let o=ve(),n=o.indexOf(B(s)),r=(n+o.length-1)%o.length,l=(n+1)%o.length,a=o[r],i=o[l];return!(null!=(e=B(v))&&e.contains(a)||null!=(t=B(v))&&t.contains(i))}),w={popoverState:a,buttonId:x(null),panelId:x(null),panel:v,button:s,isPortalled:p,beforePanelSentinel:u,afterPanelSentinel:c,togglePopover(){a.value=M(a.value,{0:1,1:0})},closePopover(){1!==a.value&&(a.value=1)},close(e){w.closePopover();let t=e?e instanceof HTMLElement?e:e.value instanceof HTMLElement?B(e):B(w.button):B(w.button);null==t||t.focus()}};Y(bo,w),I(d(()=>M(a.value,{0:A.Open,1:A.Closed})));let m={buttonId:w.buttonId,panelId:w.panelId,close(){w.closePopover()}},k=Io(),b=null==k?void 0:k.registerPopover,[y,j]=Nt(),Z=St({mainTreeNodeRef:null==k?void 0:k.mainTreeNodeRef,portals:y,defaultContainers:[s,v]});return f(()=>null==b?void 0:b(m)),ht(null==(r=h.value)?void 0:r.defaultView,"focus",e=>{var t,o;e.target!==window&&e.target instanceof HTMLElement&&0===a.value&&(function(){var e,t,o,n;return null!=(n=null==k?void 0:k.isFocusWithinPopoverGroup())?n:(null==(e=h.value)?void 0:e.activeElement)&&((null==(t=B(s))?void 0:t.contains(h.value.activeElement))||(null==(o=B(v))?void 0:o.contains(h.value.activeElement)))}()||s&&v&&(Z.contains(e.target)||null!=(t=B(w.beforePanelSentinel))&&t.contains(e.target)||null!=(o=B(w.afterPanelSentinel))&&o.contains(e.target)||w.closePopover()))},!0),C(Z.resolveContainers,(e,t)=>{var o;w.closePopover(),ie(t,se.Loose)||(e.preventDefault(),null==(o=B(s))||o.focus())},d(()=>0===a.value)),()=>{let n={open:0===a.value,close:w.close};return S(L,[S(j,{},()=>V({theirProps:i(i({},e),o),ourProps:{ref:l},slot:n,slots:t,attrs:o,name:"Popover"})),S(Z.MainTreeNode)])}}}),yo=k({name:"PopoverButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-popover-button-${F()}`,a=Mo("PopoverButton"),s=d(()=>g(a.button));n({el:a.button,$el:a.button}),y(()=>{a.buttonId.value=l}),m(()=>{a.buttonId.value=null});let c=Io(),v=null==c?void 0:c.closeOthers,h=G(Ao,null),p=d(()=>null!==h&&h.value===a.panelId.value),w=x(null),k=`headlessui-focus-sentinel-${F()}`;p.value||f(()=>{a.button.value=B(w)});let b=N(d(()=>({as:e.as,type:t.type})),w);function C(e){var t,o,n,r,l;if(p.value){if(1===a.popoverState.value)return;switch(e.key){case z.Space:case z.Enter:e.preventDefault(),null==(o=(t=e.target).click)||o.call(t),a.closePopover(),null==(n=B(a.button))||n.focus()}}else switch(e.key){case z.Space:case z.Enter:e.preventDefault(),e.stopPropagation(),1===a.popoverState.value&&(null==v||v(a.buttonId.value)),a.togglePopover();break;case z.Escape:if(0!==a.popoverState.value)return null==v?void 0:v(a.buttonId.value);if(!B(a.button)||null!=(r=s.value)&&r.activeElement&&(null==(l=B(a.button))||!l.contains(s.value.activeElement)))return;e.preventDefault(),e.stopPropagation(),a.closePopover()}}function I(e){p.value||e.key===z.Space&&e.preventDefault()}function A(t){var o,n;e.disabled||(p.value?(a.closePopover(),null==(o=B(a.button))||o.focus()):(t.preventDefault(),t.stopPropagation(),1===a.popoverState.value&&(null==v||v(a.buttonId.value)),a.togglePopover(),null==(n=B(a.button))||n.focus()))}function j(e){e.preventDefault(),e.stopPropagation()}let H=wt();function O(){let e=B(a.panel);e&&M(H.value,{[pt.Forwards]:()=>ee(e,te.First),[pt.Backwards]:()=>ee(e,te.Last)})===ne.Error&&ee(ve().filter(e=>"true"!==e.dataset.headlessuiFocusGuard),M(H.value,{[pt.Forwards]:te.Next,[pt.Backwards]:te.Previous}),{relativeTo:B(a.button)})}return()=>{let n=0===a.popoverState.value,r={open:n},s=u(e,[]),d=p.value?{ref:w,type:b.value,onKeydown:C,onClick:A}:{ref:w,id:l,type:b.value,"aria-expanded":0===a.popoverState.value,"aria-controls":B(a.panel)?a.panelId.value:void 0,disabled:!!e.disabled||void 0,onKeydown:C,onKeyup:I,onClick:A,onMousedown:j};return S(L,[V({ourProps:d,theirProps:i(i({},t),s),slot:r,attrs:t,slots:o,name:"PopoverButton"}),n&&!p.value&&a.isPortalled.value&&S(Z,{id:k,features:P.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:O})])}}}),So=k({name:"PopoverOverlay",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0}},setup(e,{attrs:t,slots:o}){let n=Mo("PopoverOverlay"),r=`headlessui-popover-overlay-${F()}`,l=_(),a=d(()=>null!==l?(l.value&A.Open)===A.Open:0===n.popoverState.value);function i(){n.closePopover()}return()=>{let l={open:0===n.popoverState.value};return V({ourProps:{id:r,"aria-hidden":!0,onClick:i},theirProps:e,slot:l,attrs:t,slots:o,features:U.RenderStrategy|U.Static,visible:a.value,name:"PopoverOverlay"})}}}),jo=k({name:"PopoverPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-popover-panel-${F()}`,{focus:a}=e,c=Mo("PopoverPanel"),v=d(()=>g(c.panel)),h=`headlessui-focus-sentinel-before-${F()}`,p=`headlessui-focus-sentinel-after-${F()}`;n({el:c.panel,$el:c.panel}),y(()=>{c.panelId.value=l}),m(()=>{c.panelId.value=null}),Y(Ao,c.panelId),f(()=>{var e,t;if(!a||0!==c.popoverState.value||!c.panel)return;let o=null==(e=v.value)?void 0:e.activeElement;null!=(t=B(c.panel))&&t.contains(o)||ee(B(c.panel),te.First)});let w=_(),k=d(()=>null!==w?(w.value&A.Open)===A.Open:0===c.popoverState.value);function x(e){var t,o;if(e.key===z.Escape){if(0!==c.popoverState.value||!B(c.panel)||v.value&&(null==(t=B(c.panel))||!t.contains(v.value.activeElement)))return;e.preventDefault(),e.stopPropagation(),c.closePopover(),null==(o=B(c.button))||o.focus()}}function b(e){var t,o,n,r,l;let a=e.relatedTarget;a&&B(c.panel)&&(null!=(t=B(c.panel))&&t.contains(a)||(c.closePopover(),(null!=(n=null==(o=B(c.beforePanelSentinel))?void 0:o.contains)&&n.call(o,a)||null!=(l=null==(r=B(c.afterPanelSentinel))?void 0:r.contains)&&l.call(r,a))&&a.focus({preventScroll:!0})))}let C=wt();function I(){let e=B(c.panel);e&&M(C.value,{[pt.Forwards]:()=>{var t;ee(e,te.First)===ne.Error&&(null==(t=B(c.afterPanelSentinel))||t.focus())},[pt.Backwards]:()=>{var e;null==(e=B(c.button))||e.focus({preventScroll:!0})}})}function j(){let e=B(c.panel);e&&M(C.value,{[pt.Forwards]:()=>{let e=B(c.button),t=B(c.panel);if(!e)return;let o=ve(),n=o.indexOf(e),r=o.slice(0,n+1),l=[...o.slice(n+1),...r];for(let a of l.slice())if("true"===a.dataset.headlessuiFocusGuard||null!=t&&t.contains(a)){let e=l.indexOf(a);-1!==e&&l.splice(e,1)}ee(l,te.First,{sorted:!1})},[pt.Backwards]:()=>{var t;ee(e,te.Previous)===ne.Error&&(null==(t=B(c.button))||t.focus())}})}return()=>{let n={open:0===c.popoverState.value,close:c.close},r=e,{focus:d}=r,v=u(r,["focus"]),w={ref:c.panel,id:l,onKeydown:x,onFocusout:a&&0===c.popoverState.value?b:void 0,tabIndex:-1};return V({ourProps:w,theirProps:i(i({},t),v),attrs:t,slot:n,slots:s(i({},o),{default:(...e)=>{var t;return[S(L,[k.value&&c.isPortalled.value&&S(Z,{id:h,ref:c.beforePanelSentinel,features:P.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:I}),null==(t=o.default)?void 0:t.call(o,...e),k.value&&c.isPortalled.value&&S(Z,{id:p,ref:c.afterPanelSentinel,features:P.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:j})])]}}),features:U.RenderStrategy|U.Static,visible:k.value,name:"PopoverPanel"})}}}),Vo=k({name:"PopoverGroup",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:t,slots:o,expose:n}){let r=x(null),l=v([]),a=d(()=>g(r)),s=function(){let e=x(null);return{mainTreeNodeRef:e,MainTreeNode:()=>S(Z,{features:P.Hidden,ref:e})}}();function u(e){let t=l.value.indexOf(e);-1!==t&&l.value.splice(t,1)}return n({el:r,$el:r}),Y(Co,{registerPopover:function(e){return l.value.push(e),()=>{u(e)}},unregisterPopover:u,isFocusWithinPopoverGroup:function(){var e;let t=a.value;if(!t)return!1;let o=t.activeElement;return!(null==(e=B(r))||!e.contains(o))||l.value.some(e=>{var n,r;return(null==(n=t.getElementById(e.buttonId.value))?void 0:n.contains(o))||(null==(r=t.getElementById(e.panelId.value))?void 0:r.contains(o))})},closeOthers:function(e){for(let t of l.value)t.buttonId.value!==e&&t.close()},mainTreeNodeRef:s.mainTreeNodeRef}),()=>S(L,[V({ourProps:{ref:r},theirProps:i(i({},e),t),slot:{},attrs:t,slots:o,name:"PopoverGroup"}),S(s.MainTreeNode)])}}),Zo=Symbol("LabelContext");function Ho(){let e=G(Zo,null);if(null===e){let e=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Ho),e}return e}function Po({slot:e={},name:t="Label",props:o={}}={}){let n=x([]);return Y(Zo,{register:function(e){return n.value.push(e),()=>{let t=n.value.indexOf(e);-1!==t&&n.value.splice(t,1)}},slot:e,name:t,props:o}),d(()=>n.value.length>0?n.value.join(" "):void 0)}let Oo=k({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:o}){var n;let r=null!=(n=e.id)?n:`headlessui-label-${F()}`,l=Ho();return y(()=>m(l.register(r))),()=>{let{name:n="Label",slot:a={},props:d={}}=l,v=e,{passive:h}=v,p=u(v,["passive"]),w=s(i({},Object.entries(d).reduce((e,[t,o])=>Object.assign(e,{[t]:c(o)}),{})),{id:r});return h&&(delete w.onClick,delete w.htmlFor,delete p.onClick),V({ourProps:w,theirProps:p,slot:a,attrs:o,slots:t,name:n})}}});function Lo(e,t){return e===t}let To=Symbol("RadioGroupContext");function Eo(e){let t=G(To,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Eo),t}return t}let Ro=k({name:"RadioGroup",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"div"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>Lo},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{emit:t,attrs:o,slots:n,expose:r}){var l;let a=null!=(l=e.id)?l:`headlessui-radiogroup-${F()}`,s=x(null),c=x([]),v=Po({name:"RadioGroupLabel"}),p=Lt({name:"RadioGroupDescription"});r({el:s,$el:s});let[w,m]=b(d(()=>e.modelValue),e=>t("update:modelValue",e),d(()=>e.defaultValue)),f={options:c,value:w,disabled:d(()=>e.disabled),firstOption:d(()=>c.value.find(e=>!e.propsRef.disabled)),containsCheckedOption:d(()=>c.value.some(t=>f.compare(T(t.propsRef.value),T(e.modelValue)))),compare(t,o){if("string"==typeof e.by){let n=e.by;return(null==t?void 0:t[n])===(null==o?void 0:o[n])}return e.by(t,o)},change(t){var o;if(e.disabled||f.compare(T(w.value),T(t)))return!1;let n=null==(o=c.value.find(e=>f.compare(T(e.propsRef.value),T(t))))?void 0:o.propsRef;return(null==n||!n.disabled)&&(m(t),!0)},registerOption(e){c.value.push(e),c.value=K(c.value,e=>e.element)},unregisterOption(e){let t=c.value.findIndex(t=>t.id===e);-1!==t&&c.value.splice(t,1)}};function k(e){if(!s.value||!s.value.contains(e.target))return;let t=c.value.filter(e=>!1===e.propsRef.disabled).map(e=>e.element);switch(e.key){case z.Enter:he(e.currentTarget);break;case z.ArrowLeft:case z.ArrowUp:if(e.preventDefault(),e.stopPropagation(),ee(t,te.Previous|te.WrapAround)===ne.Success){let e=c.value.find(e=>{var t;return e.element===(null==(t=g(s))?void 0:t.activeElement)});e&&f.change(e.propsRef.value)}break;case z.ArrowRight:case z.ArrowDown:if(e.preventDefault(),e.stopPropagation(),ee(t,te.Next|te.WrapAround)===ne.Success){let e=c.value.find(e=>{var t;return e.element===(null==(t=g(e.element))?void 0:t.activeElement)});e&&f.change(e.propsRef.value)}break;case z.Space:{e.preventDefault(),e.stopPropagation();let t=c.value.find(e=>{var t;return e.element===(null==(t=g(e.element))?void 0:t.activeElement)});t&&f.change(t.propsRef.value)}}}Y(To,f),qe({container:d(()=>B(s)),accept:e=>"radio"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let M=d(()=>{var e;return null==(e=B(s))?void 0:e.closest("form")});return y(()=>{h([M],()=>{if(M.value&&void 0!==e.defaultValue)return M.value.addEventListener("reset",t),()=>{var e;null==(e=M.value)||e.removeEventListener("reset",t)};function t(){f.change(e.defaultValue)}},{immediate:!0})}),()=>{let t=e,{disabled:r,name:l,form:d}=t,c=u(t,["disabled","name","form"]),h={ref:s,id:a,role:"radiogroup","aria-labelledby":v.value,"aria-describedby":p.value,onKeydown:k};return S(L,[...null!=l&&null!=w.value?j({[l]:w.value}).map(([e,t])=>S(Z,H({features:P.Hidden,key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:d,disabled:r,name:e,value:t}))):[],V({ourProps:h,theirProps:i(i({},o),O(c,["modelValue","defaultValue","by"])),slot:{},attrs:o,slots:n,name:"RadioGroup"})])}}});var Do,Fo=((Do=Fo||{})[Do.Empty=1]="Empty",Do[Do.Active=2]="Active",Do);let No=k({name:"RadioGroupOption",props:{as:{type:[Object,String],default:"div"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-radiogroup-option-${F()}`,a=Eo("RadioGroupOption"),i=Po({name:"RadioGroupLabel"}),s=Lt({name:"RadioGroupDescription"}),c=x(null),v=d(()=>({value:e.value,disabled:e.disabled})),h=x(1);n({el:c,$el:c});let p=d(()=>B(c));y(()=>a.registerOption({id:l,element:p,propsRef:v})),m(()=>a.unregisterOption(l));let w=d(()=>{var e;return(null==(e=a.firstOption.value)?void 0:e.id)===l}),f=d(()=>a.disabled.value||e.disabled),g=d(()=>a.compare(T(a.value.value),T(e.value))),k=d(()=>f.value?-1:g.value||!a.containsCheckedOption.value&&w.value?0:-1);function b(){var t;a.change(e.value)&&(h.value|=2,null==(t=B(c))||t.focus())}function M(){h.value|=2}function C(){h.value&=-3}return()=>{let n=e,{value:r,disabled:a}=n,d=u(n,["value","disabled"]),v={checked:g.value,disabled:f.value,active:Boolean(2&h.value)},p={id:l,ref:c,role:"radio","aria-checked":g.value?"true":"false","aria-labelledby":i.value,"aria-describedby":s.value,"aria-disabled":!!f.value||void 0,tabIndex:k.value,onClick:f.value?void 0:b,onFocus:f.value?void 0:M,onBlur:f.value?void 0:C};return V({ourProps:p,theirProps:d,slot:v,attrs:t,slots:o,name:"RadioGroupOption"})}}}),$o=Oo,zo=Tt,Uo=Symbol("GroupContext"),Go=k({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:t,attrs:o}){let n=x(null),r=Po({name:"SwitchLabel",props:{htmlFor:d(()=>{var e;return null==(e=n.value)?void 0:e.id}),onClick(e){n.value&&("LABEL"===e.currentTarget.tagName&&e.preventDefault(),n.value.click(),n.value.focus({preventScroll:!0}))}}}),l=Lt({name:"SwitchDescription"});return Y(Uo,{switchRef:n,labelledby:r,describedby:l}),()=>V({theirProps:e,ourProps:{},slot:{},slots:t,attrs:o,name:"SwitchGroup"})}}),Wo=k({name:"Switch",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(e,{emit:t,attrs:o,slots:n,expose:r}){var l;let a=null!=(l=e.id)?l:`headlessui-switch-${F()}`,s=G(Uo,null),[c,v]=b(d(()=>e.modelValue),e=>t("update:modelValue",e),d(()=>e.defaultChecked));function p(){v(!c.value)}let w=x(null),m=null===s?w:s.switchRef,f=N(d(()=>({as:e.as,type:o.type})),m);function g(e){e.preventDefault(),p()}function k(e){e.key===z.Space?(e.preventDefault(),p()):e.key===z.Enter&&he(e.currentTarget)}function M(e){e.preventDefault()}r({el:m,$el:m});let C=d(()=>{var e,t;return null==(t=null==(e=B(m))?void 0:e.closest)?void 0:t.call(e,"form")});return y(()=>{h([C],()=>{if(C.value&&void 0!==e.defaultChecked)return C.value.addEventListener("reset",t),()=>{var e;null==(e=C.value)||e.removeEventListener("reset",t)};function t(){v(e.defaultChecked)}},{immediate:!0})}),()=>{let t=e,{name:r,value:l,form:d,tabIndex:v}=t,h=u(t,["name","value","form","tabIndex"]),p={checked:c.value},w={id:a,ref:m,role:"switch",type:f.value,tabIndex:-1===v?0:v,"aria-checked":c.value,"aria-labelledby":null==s?void 0:s.labelledby.value,"aria-describedby":null==s?void 0:s.describedby.value,onClick:g,onKeyup:k,onKeypress:M};return S(L,[null!=r&&null!=c.value?S(Z,H({features:P.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:c.value,form:d,disabled:h.disabled,name:r,value:l})):null,V({ourProps:w,theirProps:i(i({},o),O(h,["modelValue","defaultChecked"])),slot:p,attrs:o,slots:n,name:"Switch"})])}}}),qo=Oo,_o=Tt,Ko=k({props:{onFocus:{type:Function,required:!0}},setup(e){let t=x(!0);return()=>t.value?S(Z,{as:"button",type:"button",features:P.Focusable,onFocus(o){o.preventDefault();let n,r=50;n=requestAnimationFrame(function o(){var l;if(!(r--<=0))return null!=(l=e.onFocus)&&l.call(e)?(t.value=!1,void cancelAnimationFrame(n)):void(n=requestAnimationFrame(o));n&&cancelAnimationFrame(n)})}}):null}});var Yo,Xo=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(Xo||{}),Qo=((Yo=Qo||{})[Yo.Less=-1]="Less",Yo[Yo.Equal=0]="Equal",Yo[Yo.Greater=1]="Greater",Yo);let Jo=Symbol("TabsContext");function en(e){let t=G(Jo,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,en),t}return t}let tn=Symbol("TabsSSRContext"),on=k({name:"TabGroup",emits:{change:e=>!0},props:{as:{type:[Object,String],default:"template"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:t,attrs:o,emit:n}){var r;let l=x(null!=(r=e.selectedIndex)?r:e.defaultIndex),a=x([]),s=x([]),u=d(()=>null!==e.selectedIndex),c=d(()=>u.value?e.selectedIndex:l.value);function v(e){var t;let o=K(p.tabs.value,B),n=K(p.panels.value,B),r=o.filter(e=>{var t;return!(null!=(t=B(e))&&t.hasAttribute("disabled"))});if(e<0||e>o.length-1){let t=M(null===l.value?0:Math.sign(e-l.value),{[-1]:()=>1,0:()=>M(Math.sign(e),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0}),a=M(t,{0:()=>o.indexOf(r[0]),1:()=>o.indexOf(r[r.length-1])});-1!==a&&(l.value=a),p.tabs.value=o,p.panels.value=n}else{let a=o.slice(0,e),i=[...o.slice(e),...a].find(e=>r.includes(e));if(!i)return;let s=null!=(t=o.indexOf(i))?t:p.selectedIndex.value;-1===s&&(s=p.selectedIndex.value),l.value=s,p.tabs.value=o,p.panels.value=n}}let p={selectedIndex:d(()=>{var t,o;return null!=(o=null!=(t=l.value)?t:e.defaultIndex)?o:null}),orientation:d(()=>e.vertical?"vertical":"horizontal"),activation:d(()=>e.manual?"manual":"auto"),tabs:a,panels:s,setSelectedIndex(e){c.value!==e&&n("change",e),u.value||v(e)},registerTab(e){var t;if(a.value.includes(e))return;let o=a.value[l.value];if(a.value.push(e),a.value=K(a.value,B),!u.value){let e=null!=(t=a.value.indexOf(o))?t:l.value;-1!==e&&(l.value=e)}},unregisterTab(e){let t=a.value.indexOf(e);-1!==t&&a.value.splice(t,1)},registerPanel(e){s.value.includes(e)||(s.value.push(e),s.value=K(s.value,B))},unregisterPanel(e){let t=s.value.indexOf(e);-1!==t&&s.value.splice(t,1)}};Y(Jo,p);let w=x({tabs:[],panels:[]}),m=x(!1);y(()=>{m.value=!0}),Y(tn,d(()=>m.value?null:w.value));let g=d(()=>e.selectedIndex);return y(()=>{h([g],()=>{var t;return v(null!=(t=e.selectedIndex)?t:e.defaultIndex)},{immediate:!0})}),f(()=>{if(!u.value||null==c.value||p.tabs.value.length<=0)return;let e=K(p.tabs.value,B);e.some((e,t)=>B(p.tabs.value[t])!==B(e))&&p.setSelectedIndex(e.findIndex(e=>B(e)===B(p.tabs.value[c.value])))}),()=>{let n={selectedIndex:l.value};return S(L,[a.value.length<=0&&S(Ko,{onFocus:()=>{for(let e of a.value){let t=B(e);if(0===(null==t?void 0:t.tabIndex))return t.focus(),!0}return!1}}),V({theirProps:i(i({},o),O(e,["selectedIndex","defaultIndex","manual","vertical","onChange"])),ourProps:{},slot:n,slots:t,attrs:o,name:"TabGroup"})])}}}),nn=k({name:"TabList",props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:t,slots:o}){let n=en("TabList");return()=>{let r={selectedIndex:n.selectedIndex.value},l={role:"tablist","aria-orientation":n.orientation.value};return V({ourProps:l,theirProps:e,slot:r,attrs:t,slots:o,name:"TabList"})}}}),rn=k({name:"Tab",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-tabs-tab-${F()}`,a=en("Tab"),i=x(null);n({el:i,$el:i}),y(()=>a.registerTab(i)),m(()=>a.unregisterTab(i));let s=G(tn),c=d(()=>{if(s.value){let e=s.value.tabs.indexOf(l);return-1===e?s.value.tabs.push(l)-1:e}return-1}),v=d(()=>{let e=a.tabs.value.indexOf(i);return-1===e?c.value:e}),h=d(()=>v.value===a.selectedIndex.value);function p(e){var t;let o=e();if(o===ne.Success&&"auto"===a.activation.value){let e=null==(t=g(i))?void 0:t.activeElement,o=a.tabs.value.findIndex(t=>B(t)===e);-1!==o&&a.setSelectedIndex(o)}return o}function w(e){let t=a.tabs.value.map(e=>B(e)).filter(Boolean);if(e.key===z.Space||e.key===z.Enter)return e.preventDefault(),e.stopPropagation(),void a.setSelectedIndex(v.value);switch(e.key){case z.Home:case z.PageUp:return e.preventDefault(),e.stopPropagation(),p(()=>ee(t,te.First));case z.End:case z.PageDown:return e.preventDefault(),e.stopPropagation(),p(()=>ee(t,te.Last))}return p(()=>M(a.orientation.value,{vertical:()=>e.key===z.ArrowUp?ee(t,te.Previous|te.WrapAround):e.key===z.ArrowDown?ee(t,te.Next|te.WrapAround):ne.Error,horizontal:()=>e.key===z.ArrowLeft?ee(t,te.Previous|te.WrapAround):e.key===z.ArrowRight?ee(t,te.Next|te.WrapAround):ne.Error}))===ne.Success?e.preventDefault():void 0}let f=x(!1);function k(){var t;f.value||(f.value=!0,!e.disabled&&(null==(t=B(i))||t.focus({preventScroll:!0}),a.setSelectedIndex(v.value),Ue(()=>{f.value=!1})))}function b(e){e.preventDefault()}let C=N(d(()=>({as:e.as,type:t.type})),i);return()=>{var n,r;let s={selected:h.value,disabled:null!=(n=e.disabled)&&n},d=u(e,[]),c={ref:i,onKeydown:w,onMousedown:b,onClick:k,id:l,role:"tab",type:C.value,"aria-controls":null==(r=B(a.panels.value[v.value]))?void 0:r.id,"aria-selected":h.value,tabIndex:h.value?0:-1,disabled:!!e.disabled||void 0};return V({ourProps:c,theirProps:d,slot:s,attrs:t,slots:o,name:"Tab"})}}}),ln=k({name:"TabPanels",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:o}){let n=en("TabPanels");return()=>{let r={selectedIndex:n.selectedIndex.value};return V({theirProps:e,ourProps:{},slot:r,attrs:o,slots:t,name:"TabPanels"})}}}),an=k({name:"TabPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null},tabIndex:{type:Number,default:0}},setup(e,{attrs:t,slots:o,expose:n}){var r;let l=null!=(r=e.id)?r:`headlessui-tabs-panel-${F()}`,a=en("TabPanel"),s=x(null);n({el:s,$el:s}),y(()=>a.registerPanel(s)),m(()=>a.unregisterPanel(s));let c=G(tn),v=d(()=>{if(c.value){let e=c.value.panels.indexOf(l);return-1===e?c.value.panels.push(l)-1:e}return-1}),h=d(()=>{let e=a.panels.value.indexOf(s);return-1===e?v.value:e}),p=d(()=>h.value===a.selectedIndex.value);return()=>{var n;let r={selected:p.value},d=e,{tabIndex:c}=d,v=u(d,["tabIndex"]),w={ref:s,id:l,role:"tabpanel","aria-labelledby":null==(n=B(a.tabs.value[h.value]))?void 0:n.id,tabIndex:p.value?c:-1};return p.value||!e.unmount||e.static?V({ourProps:w,theirProps:v,slot:r,attrs:t,slots:o,features:U.Static|U.RenderStrategy,visible:p.value,name:"TabPanel"}):S(Z,i({as:"span","aria-hidden":!0},w))}}});function sn(e,...t){e&&t.length>0&&e.classList.add(...t)}function un(e,...t){e&&t.length>0&&e.classList.remove(...t)}var dn=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(dn||{});function cn(e,t,o,n,r,l){let a=Ge(),i=void 0!==l?function(e){let t={called:!1};return(...o)=>{if(!t.called)return t.called=!0,e(...o)}}(l):()=>{};return un(e,...r),sn(e,...t,...o),a.nextFrame(()=>{un(e,...o),sn(e,...n),a.add(function(e,t){let o=Ge();if(!e)return o.dispose;let{transitionDuration:n,transitionDelay:r}=getComputedStyle(e),[l,a]=[n,r].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t});return 0!==l?o.setTimeout(()=>t("finished"),l+a):t("finished"),o.add(()=>t("cancelled")),o.dispose}(e,o=>(un(e,...n,...t),sn(e,...r),i(o))))}),a.add(()=>un(e,...t,...o,...n,...r)),a.add(()=>i("cancelled")),a.dispose}function vn(e=""){return e.split(/\s+/).filter(e=>e.length>1)}let hn=Symbol("TransitionContext");var pn,wn=((pn=wn||{}).Visible="visible",pn.Hidden="hidden",pn);let mn=Symbol("NestingContext");function fn(e){return"children"in e?fn(e.children):e.value.filter(({state:e})=>"visible"===e).length>0}function gn(e){let t=x([]),o=x(!1);function n(n,r=we.Hidden){let l=t.value.findIndex(({id:e})=>e===n);-1!==l&&(M(r,{[we.Unmount](){t.value.splice(l,1)},[we.Hidden](){t.value[l].state="hidden"}}),!fn(t)&&o.value&&(null==e||e()))}return y(()=>o.value=!0),m(()=>o.value=!1),{children:t,register:function(e){let o=t.value.find(({id:t})=>t===e);return o?"visible"!==o.state&&(o.state="visible"):t.value.push({id:e,state:"visible"}),()=>n(e,we.Unmount)},unregister:n}}let kn=U.RenderStrategy,xn=k({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:o,slots:n,expose:r}){let l=x(0);function a(){l.value|=A.Opening,t("beforeEnter")}function c(){l.value&=~A.Opening,t("afterEnter")}function v(){l.value|=A.Closing,t("beforeLeave")}function p(){l.value&=~A.Closing,t("afterLeave")}if(null===G(hn,null)&&pe())return()=>S(Mn,s(i({},e),{onBeforeEnter:a,onAfterEnter:c,onBeforeLeave:v,onAfterLeave:p}),n);let w=x(null),g=d(()=>e.unmount?we.Unmount:we.Hidden);r({el:w,$el:w});let{show:k,appear:b}=function(){let e=G(hn,null);if(null===e)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}(),{register:C,unregister:j}=function(){let e=G(mn,null);if(null===e)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}(),Z=x(k.value?"visible":"hidden"),H={value:!0},P=F(),O={value:!1},L=gn(()=>{!O.value&&"hidden"!==Z.value&&(Z.value="hidden",j(P),p())});y(()=>{let e=C(P);m(e)}),f(()=>{if(g.value===we.Hidden&&P){if(k.value&&"visible"!==Z.value)return void(Z.value="visible");M(Z.value,{hidden:()=>j(P),visible:()=>C(P)})}});let T=vn(e.enter),E=vn(e.enterFrom),R=vn(e.enterTo),D=vn(e.entered),N=vn(e.leave),$=vn(e.leaveFrom),z=vn(e.leaveTo);return y(()=>{f(()=>{if("visible"===Z.value){let e=B(w);if(e instanceof Comment&&""===e.data)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})}),y(()=>{h([k],(e,t,o)=>{(function(e){let t=H.value&&!b.value,o=B(w);!o||!(o instanceof HTMLElement)||t||(O.value=!0,k.value&&a(),k.value||v(),e(k.value?cn(o,T,E,R,D,e=>{O.value=!1,e===dn.Finished&&c()}):cn(o,N,$,z,D,e=>{O.value=!1,e===dn.Finished&&(fn(L)||(Z.value="hidden",j(P),p()))})))})(o),H.value=!1},{immediate:!0})}),Y(mn,L),I(d(()=>M(Z.value,{visible:A.Open,hidden:A.Closed})|l.value)),()=>{let t=e,{appear:r,show:l,enter:a,enterFrom:s,enterTo:d,entered:c,leave:v,leaveFrom:h,leaveTo:p}=t,m=u(t,["appear","show","enter","enterFrom","enterTo","entered","leave","leaveFrom","leaveTo"]),f={ref:w},g=i(i({},m),b.value&&k.value&&Q.isServer?{class:me([o.class,m.class,...T,...E])}:{});return V({theirProps:g,ourProps:f,slot:{},slots:n,attrs:o,features:kn,visible:"visible"===Z.value,name:"TransitionChild"})}}}),bn=xn,Mn=k({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:o,slots:n}){let r=_(),l=d(()=>null===e.show&&null!==r?(r.value&A.Open)===A.Open:e.show);f(()=>{if(![!0,!1].includes(l.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let a=x(l.value?"visible":"hidden"),u=gn(()=>{a.value="hidden"}),c=x(!0),v={show:l,appear:d(()=>e.appear||!c.value)};return y(()=>{f(()=>{c.value=!1,l.value?a.value="visible":fn(u)||(a.value="hidden")})}),Y(mn,u),Y(hn,v),()=>{let r=O(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),l={unmount:e.unmount};return V({ourProps:s(i({},l),{as:"template"}),theirProps:{},slot:{},slots:s(i({},n),{default:()=>[S(bn,i(i(i({onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave")},o),l),r),n.default)]}),attrs:{},features:kn,visible:"visible"===a.value,name:"Transition"})}}});const Cn=Object.freeze(Object.defineProperty({__proto__:null,Combobox:it,ComboboxButton:ut,ComboboxInput:dt,ComboboxLabel:st,ComboboxOption:vt,ComboboxOptions:ct,Dialog:Kt,DialogBackdrop:Xt,DialogDescription:eo,DialogOverlay:Yt,DialogPanel:Qt,DialogTitle:Jt,Disclosure:ao,DisclosureButton:io,DisclosurePanel:so,FocusTrap:kt,Listbox:fe,ListboxButton:ge,ListboxLabel:ke,ListboxOption:xe,ListboxOptions:be,Menu:wo,MenuButton:mo,MenuItem:go,MenuItems:fo,Popover:Bo,PopoverButton:yo,PopoverGroup:Vo,PopoverOverlay:So,PopoverPanel:jo,Portal:Dt,PortalGroup:zt,RadioGroup:Ro,RadioGroupDescription:zo,RadioGroupLabel:$o,RadioGroupOption:No,Switch:Wo,SwitchDescription:_o,SwitchGroup:Go,SwitchLabel:qo,Tab:rn,TabGroup:on,TabList:nn,TabPanel:an,TabPanels:ln,TransitionChild:xn,TransitionRoot:Mn,provideUseId:Me},Symbol.toStringTag,{value:"Module"}));const In=Object.freeze(Object.defineProperty({__proto__:null,AcademicCapIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-********* 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"})])},AdjustmentsHorizontalIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"})])},AdjustmentsVerticalIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 13.5V3.75m0 9.75a1.5 1.5 0 0 1 0 3m0-3a1.5 1.5 0 0 0 0 3m0 3.75V16.5m12-3V3.75m0 9.75a1.5 1.5 0 0 1 0 3m0-3a1.5 1.5 0 0 0 0 3m0 3.75V16.5m-6-9V3.75m0 3.75a1.5 1.5 0 0 1 0 3m0-3a1.5 1.5 0 0 0 0 3m0 9.75V10.5"})])},ArchiveBoxArrowDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m8.25 3v6.75m0 0-3-3m3 3 3-3M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])},ArchiveBoxIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])},ArchiveBoxXMarkIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])},ArrowDownCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9 12.75 3 3m0 0 3-3m-3 3v-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},ArrowDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"})])},ArrowDownLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m19.5 4.5-15 15m0 0h11.25m-11.25 0V8.25"})])},ArrowDownOnSquareIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 8.25H7.5a2.25 2.25 0 0 0-2.25 2.25v9a2.25 2.25 0 0 0 2.25 2.25h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25H15M9 12l3 3m0 0 3-3m-3 3V2.25"})])},ArrowDownOnSquareStackIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.5 7.5h-.75A2.25 2.25 0 0 0 4.5 9.75v7.5a2.25 2.25 0 0 0 2.25 2.25h7.5a2.25 2.25 0 0 0 2.25-2.25v-7.5a2.25 2.25 0 0 0-2.25-2.25h-.75m-6 3.75 3 3m0 0 3-3m-3 3V1.5m6 9h.75a2.25 2.25 0 0 1 2.25 2.25v7.5a2.25 2.25 0 0 1-2.25 2.25h-7.5a2.25 2.25 0 0 1-2.25-2.25v-.75"})])},ArrowDownRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 4.5 15 15m0 0V8.25m0 11.25H8.25"})])},ArrowDownTrayIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"})])},ArrowLeftCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m11.25 9-3 3m0 0 3 3m-3-3h7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},ArrowLeftEndOnRectangleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h12.75"})])},ArrowLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"})])},ArrowLeftOnRectangleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h12.75"})])},ArrowLeftStartOnRectangleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 9V5.25A2.25 2.25 0 0 1 10.5 3h6a2.25 2.25 0 0 1 2.25 2.25v13.5A2.25 2.25 0 0 1 16.5 21h-6a2.25 2.25 0 0 1-2.25-2.25V15m-3 0-3-3m0 0 3-3m-3 3H15"})])},ArrowLongDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 17.25 12 21m0 0-3.75-3.75M12 21V3"})])},ArrowLongLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"})])},ArrowLongRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3"})])},ArrowLongUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 6.75 12 3m0 0 3.75 3.75M12 3v18"})])},ArrowPathIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"})])},ArrowPathRoundedSquareIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 0 0-3.7-3.7 48.678 48.678 0 0 0-7.324 0 4.006 4.006 0 0 0-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 0 0 3.7 3.7 48.656 48.656 0 0 0 7.324 0 4.006 4.006 0 0 0 3.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3-3 3"})])},ArrowRightCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},ArrowRightEndOnRectangleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 9V5.25A2.25 2.25 0 0 1 10.5 3h6a2.25 2.25 0 0 1 2.25 2.25v13.5A2.25 2.25 0 0 1 16.5 21h-6a2.25 2.25 0 0 1-2.25-2.25V15M12 9l3 3m0 0-3 3m3-3H2.25"})])},ArrowRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"})])},ArrowRightOnRectangleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"})])},ArrowRightStartOnRectangleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"})])},ArrowSmallDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m0 0 6.75-6.75M12 19.5l-6.75-6.75"})])},ArrowSmallLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 12h-15m0 0 6.75 6.75M4.5 12l6.75-6.75"})])},ArrowSmallRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 12h15m0 0-6.75-6.75M19.5 12l-6.75 6.75"})])},ArrowSmallUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 19.5v-15m0 0-6.75 6.75M12 4.5l6.75 6.75"})])},ArrowTopRightOnSquareIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"})])},ArrowTrendingDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"})])},ArrowTrendingUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"})])},ArrowTurnDownLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m7.49 12-3.75 3.75m0 0 3.75 3.75m-3.75-3.75h16.5V4.499"})])},ArrowTurnDownRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m16.49 12 3.75 3.75m0 0-3.75 3.75m3.75-3.75H3.74V4.499"})])},ArrowTurnLeftDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m11.99 16.5-3.75 3.75m0 0L4.49 16.5m3.75 3.75V3.75h11.25"})])},ArrowTurnLeftUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.99 7.5 8.24 3.75m0 0L4.49 7.5m3.75-3.75v16.499h11.25"})])},ArrowTurnRightDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m11.99 16.5 3.75 3.75m0 0 3.75-3.75m-3.75 3.75V3.75H4.49"})])},ArrowTurnRightUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m11.99 7.5 3.75-3.75m0 0 3.75 3.75m-3.75-3.75v16.499H4.49"})])},ArrowTurnUpLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.49 12 3.74 8.248m0 0 3.75-3.75m-3.75 3.75h16.5V19.5"})])},ArrowTurnUpRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m16.49 12 3.75-3.751m0 0-3.75-3.75m3.75 3.75H3.74V19.5"})])},ArrowUpCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15 11.25-3-3m0 0-3 3m3-3v7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},ArrowUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"})])},ArrowUpLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m19.5 19.5-15-15m0 0v11.25m0-11.25h11.25"})])},ArrowUpOnSquareIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 8.25H7.5a2.25 2.25 0 0 0-2.25 2.25v9a2.25 2.25 0 0 0 2.25 2.25h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25H15m0-3-3-3m0 0-3 3m3-3V15"})])},ArrowUpOnSquareStackIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.5 7.5h-.75A2.25 2.25 0 0 0 4.5 9.75v7.5a2.25 2.25 0 0 0 2.25 2.25h7.5a2.25 2.25 0 0 0 2.25-2.25v-7.5a2.25 2.25 0 0 0-2.25-2.25h-.75m0-3-3-3m0 0-3 3m3-3v11.25m6-2.25h.75a2.25 2.25 0 0 1 2.25 2.25v7.5a2.25 2.25 0 0 1-2.25 2.25h-7.5a2.25 2.25 0 0 1-2.25-2.25v-.75"})])},ArrowUpRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 19.5 15-15m0 0H8.25m11.25 0v11.25"})])},ArrowUpTrayIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"})])},ArrowUturnDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15 15-6 6m0 0-6-6m6 6V9a6 6 0 0 1 12 0v3"})])},ArrowUturnLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3"})])},ArrowUturnRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15 15 6-6m0 0-6-6m6 6H9a6 6 0 0 0 0 12h3"})])},ArrowUturnUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9 9 6-6m0 0 6 6m-6-6v12a6 6 0 0 1-12 0v-3"})])},ArrowsPointingInIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 9V4.5M9 9H4.5M9 9 3.75 3.75M9 15v4.5M9 15H4.5M9 15l-5.25 5.25M15 9h4.5M15 9V4.5M15 9l5.25-5.25M15 15h4.5M15 15v4.5m0-4.5 5.25 5.25"})])},ArrowsPointingOutIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"})])},ArrowsRightLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"})])},ArrowsUpDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"})])},AtSymbolIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 12a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0Zm0 0c0 1.657 1.007 3 2.25 3S21 13.657 21 12a9 9 0 1 0-2.636 6.364M16.5 12V8.25"})])},BackspaceIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9.75 14.25 12m0 0 2.25 2.25M14.25 12l2.25-2.25M14.25 12 12 14.25m-2.58 4.92-6.374-6.375a1.125 1.125 0 0 1 0-1.59L9.42 4.83c.21-.211.497-.33.795-.33H19.5a2.25 2.25 0 0 1 2.25 2.25v10.5a2.25 2.25 0 0 1-2.25 2.25h-9.284c-.298 0-.585-.119-.795-.33Z"})])},BackwardIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 16.811c0 .864-.933 1.406-1.683.977l-7.108-4.061a1.125 1.125 0 0 1 0-1.954l7.108-4.061A1.125 1.125 0 0 1 21 8.689v8.122ZM11.25 16.811c0 .864-.933 1.406-1.683.977l-7.108-4.061a1.125 1.125 0 0 1 0-1.954l7.108-4.061a1.125 1.125 0 0 1 1.683.977v8.122Z"})])},BanknotesIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"})])},Bars2Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 9h16.5m-16.5 6.75h16.5"})])},Bars3BottomLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25H12"})])},Bars3BottomRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6.75h16.5M3.75 12h16.5M12 17.25h8.25"})])},Bars3CenterLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6.75h16.5M3.75 12H12m-8.25 5.25h16.5"})])},Bars3Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"})])},Bars4Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 5.25h16.5m-16.5 4.5h16.5m-16.5 4.5h16.5m-16.5 4.5h16.5"})])},BarsArrowDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 4.5h14.25M3 9h9.75M3 13.5h9.75m4.5-4.5v12m0 0-3.75-3.75M17.25 21 21 17.25"})])},BarsArrowUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 4.5h14.25M3 9h9.75M3 13.5h5.25m5.25-.75L17.25 9m0 0L21 12.75M17.25 9v12"})])},Battery0Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 10.5h.375c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125H21M3.75 18h15A2.25 2.25 0 0 0 21 15.75v-6a2.25 2.25 0 0 0-2.25-2.25h-15A2.25 2.25 0 0 0 1.5 9.75v6A2.25 2.25 0 0 0 3.75 18Z"})])},Battery100Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 10.5h.375c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125H21M4.5 10.5H18V15H4.5v-4.5ZM3.75 18h15A2.25 2.25 0 0 0 21 15.75v-6a2.25 2.25 0 0 0-2.25-2.25h-15A2.25 2.25 0 0 0 1.5 9.75v6A2.25 2.25 0 0 0 3.75 18Z"})])},Battery50Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 10.5h.375c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125H21M4.5 10.5h6.75V15H4.5v-4.5ZM3.75 18h15A2.25 2.25 0 0 0 21 15.75v-6a2.25 2.25 0 0 0-2.25-2.25h-15A2.25 2.25 0 0 0 1.5 9.75v6A2.25 2.25 0 0 0 3.75 18Z"})])},BeakerIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 0 1 4.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0 1 12 15a9.065 9.065 0 0 0-6.23-.693L5 14.5m14.8.8 1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0 1 12 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"})])},BellAlertIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M3.124 7.5A8.969 8.969 0 0 1 5.292 3m13.416 0a8.969 8.969 0 0 1 2.168 4.5"})])},BellIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"})])},BellSlashIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.143 17.082a24.248 24.248 0 0 0 3.844.148m-3.844-.148a23.856 23.856 0 0 1-5.455-1.31 8.964 8.964 0 0 0 2.3-5.542m3.155 6.852a3 3 0 0 0 5.667 1.97m1.965-2.277L21 21m-4.225-4.225a23.81 23.81 0 0 0 3.536-1.003A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6.53 6.53m10.245 10.245L6.53 6.53M3 3l3.53 3.53"})])},BellSnoozeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M10.5 8.25h3l-3 4.5h3"})])},BoldIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linejoin":"round",d:"M6.75 3.744h-.753v8.25h7.125a4.125 4.125 0 0 0 0-8.25H6.75Zm0 0v.38m0 16.122h6.747a4.5 4.5 0 0 0 0-9.001h-7.5v9h.753Zm0 0v-.37m0-15.751h6a3.75 3.75 0 1 1 0 7.5h-6m0-7.5v7.5m0 0v8.25m0-8.25h6.375a4.125 4.125 0 0 1 0 8.25H6.75m.747-15.38h4.875a3.375 3.375 0 0 1 0 6.75H7.497v-6.75Zm0 7.5h5.25a3.75 3.75 0 0 1 0 7.5h-5.25v-7.5Z"})])},BoltIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"})])},BoltSlashIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.412 15.655 9.75 21.75l3.745-4.012M9.257 13.5H3.75l2.659-2.849m2.048-2.194L14.25 2.25 12 10.5h8.25l-4.707 5.043M8.457 8.457 3 3m5.457 5.457 7.086 7.086m0 0L21 21"})])},BookOpenIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"})])},BookmarkIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"})])},BookmarkSlashIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m3 3 1.664 1.664M21 21l-1.5-1.5m-5.485-1.242L12 17.25 4.5 21V8.742m.164-4.078a2.15 2.15 0 0 1 1.743-1.342 48.507 48.507 0 0 1 11.186 0c1.1.128 1.907 1.077 1.907 2.185V19.5M4.664 4.664 19.5 19.5"})])},BookmarkSquareIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9"})])},BriefcaseIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"})])},BugAntIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 12.75c1.148 0 2.278.08 3.383.237 1.037.146 1.866.966 1.866 2.013 0 3.728-2.35 6.75-5.25 6.75S6.75 18.728 6.75 15c0-1.046.83-1.867 1.866-2.013A24.204 24.204 0 0 1 12 12.75Zm0 0c2.883 0 5.647.508 8.207 1.44a23.91 23.91 0 0 1-1.152 6.06M12 12.75c-2.883 0-5.647.508-8.208 1.44.125 2.104.52 4.136 1.153 6.06M12 12.75a2.25 2.25 0 0 0 2.248-2.354M12 12.75a2.25 2.25 0 0 1-2.248-2.354M12 8.25c.995 0 1.971-.08 2.922-.236.403-.066.74-.358.795-.762a3.778 3.778 0 0 0-.399-2.25M12 8.25c-.995 0-1.97-.08-2.922-.236-.402-.066-.74-.358-.795-.762a3.734 3.734 0 0 1 .4-2.253M12 8.25a2.25 2.25 0 0 0-2.248 2.146M12 8.25a2.25 2.25 0 0 1 2.248 2.146M8.683 5a6.032 6.032 0 0 1-1.155-1.002c.07-.63.27-1.222.574-1.747m.581 2.749A3.75 3.75 0 0 1 15.318 5m0 0c.427-.283.815-.62 1.155-.999a4.471 4.471 0 0 0-.575-1.752M4.921 6a24.048 24.048 0 0 0-.392 3.314c1.668.546 3.416.914 5.223 1.082M19.08 6c.205 1.08.337 2.187.392 3.314a23.882 23.882 0 0 1-5.223 1.082"})])},BuildingLibraryIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z"})])},BuildingOffice2Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"})])},BuildingOfficeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"})])},BuildingStorefrontIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"})])},CakeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 8.25v-1.5m0 1.5c-1.355 0-2.697.056-4.024.166C6.845 8.51 6 9.473 6 10.608v2.513m6-4.871c1.355 0 2.697.056 4.024.166C17.155 8.51 18 9.473 18 10.608v2.513M15 8.25v-1.5m-6 1.5v-1.5m12 9.75-1.5.75a3.354 3.354 0 0 1-3 0 3.354 3.354 0 0 0-3 0 3.354 3.354 0 0 1-3 0 3.354 3.354 0 0 0-3 0 3.354 3.354 0 0 1-3 0L3 16.5m15-3.379a48.474 48.474 0 0 0-6-.371c-2.032 0-4.034.126-6 .371m12 0c.39.049.777.102 1.163.16 1.07.16 1.837 1.094 1.837 2.175v5.169c0 .621-.504 1.125-1.125 1.125H4.125A1.125 1.125 0 0 1 3 20.625v-5.17c0-1.08.768-2.014 1.837-2.174A47.78 47.78 0 0 1 6 13.12M12.265 3.11a.375.375 0 1 1-.53 0L12 2.845l.265.265Zm-3 0a.375.375 0 1 1-.53 0L9 2.845l.265.265Zm6 0a.375.375 0 1 1-.53 0L15 2.845l.265.265Z"})])},CalculatorIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 15.75V18m-7.5-6.75h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V13.5Zm0 2.25h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V18Zm2.498-6.75h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V13.5Zm0 2.25h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V18Zm2.504-6.75h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V13.5Zm0 2.25h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V18Zm2.498-6.75h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V13.5ZM8.25 6h7.5v2.25h-7.5V6ZM12 2.25c-1.892 0-3.758.11-5.593.322C5.307 2.7 4.5 3.65 4.5 4.757V19.5a2.25 2.25 0 0 0 2.25 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25V4.757c0-1.108-.806-2.057-1.907-2.185A48.507 48.507 0 0 0 12 2.25Z"})])},CalendarDateRangeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 2.994v2.25m10.5-2.25v2.25m-14.252 13.5V7.491a2.25 2.25 0 0 1 2.25-2.25h13.5a2.25 2.25 0 0 1 2.25 2.25v11.251m-18 0a2.25 2.25 0 0 0 2.25 2.25h13.5a2.25 2.25 0 0 0 2.25-2.25m-18 0v-7.5a2.25 2.25 0 0 1 2.25-2.25h13.5a2.25 2.25 0 0 1 2.25 2.25v7.5m-6.75-6h2.25m-9 2.25h4.5m.002-2.25h.005v.006H12v-.006Zm-.001 4.5h.006v.006h-.006v-.005Zm-2.25.001h.005v.006H9.75v-.006Zm-2.25 0h.005v.005h-.006v-.005Zm6.75-2.247h.005v.005h-.005v-.005Zm0 2.247h.006v.006h-.006v-.006Zm2.25-2.248h.006V15H16.5v-.005Z"})])},CalendarDaysIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"})])},CalendarIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])},CameraIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"})])},ChartBarIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"})])},ChartBarSquareIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0 0 20.25 18V6A2.25 2.25 0 0 0 18 3.75H6A2.25 2.25 0 0 0 3.75 6v12A2.25 2.25 0 0 0 6 20.25Z"})])},ChartPieIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"})])},ChatBubbleBottomCenterIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.068.157 2.148.279 3.238.364.466.037.893.281 1.153.671L12 21l2.652-3.978c.26-.39.687-.634 1.153-.67 1.09-.086 2.17-.208 3.238-.365 1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"})])},ChatBubbleBottomCenterTextIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.************.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"})])},ChatBubbleLeftEllipsisIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"})])},ChatBubbleLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"})])},ChatBubbleLeftRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"})])},ChatBubbleOvalLeftEllipsisIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.625 12a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 0 1-2.555-.337A5.972 5.972 0 0 1 5.41 20.97a5.969 5.969 0 0 1-.474-.065 4.48 4.48 0 0 0 .978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25Z"})])},ChatBubbleOvalLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 0 1-.923 1.785A5.969 5.969 0 0 0 6 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337Z"})])},CheckBadgeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"})])},CheckCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},CheckIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 12.75 6 6 9-13.5"})])},ChevronDoubleDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 5.25 7.5 7.5 7.5-7.5m-15 6 7.5 7.5 7.5-7.5"})])},ChevronDoubleLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m18.75 4.5-7.5 7.5 7.5 7.5m-6-15L5.25 12l7.5 7.5"})])},ChevronDoubleRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m5.25 4.5 7.5 7.5-7.5 7.5m6-15 7.5 7.5-7.5 7.5"})])},ChevronDoubleUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 18.75 7.5-7.5 7.5 7.5"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 12.75 7.5-7.5 7.5 7.5"})])},ChevronDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"})])},ChevronLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 19.5 8.25 12l7.5-7.5"})])},ChevronRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"})])},ChevronUpDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"})])},ChevronUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"})])},CircleStackIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"})])},ClipboardDocumentCheckIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0 1 18 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3 1.5 1.5 3-3.75"})])},ClipboardDocumentIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z"})])},ClipboardDocumentListIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"})])},ClipboardIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184"})])},ClockIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},CloudArrowDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"})])},CloudArrowUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"})])},CloudIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"})])},CodeBracketIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5"})])},CodeBracketSquareIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.25 9.75 16.5 12l-2.25 2.25m-4.5 0L7.5 12l2.25-2.25M6 20.25h12A2.25 2.25 0 0 0 20.25 18V6A2.25 2.25 0 0 0 18 3.75H6A2.25 2.25 0 0 0 3.75 6v12A2.25 2.25 0 0 0 6 20.25Z"})])},Cog6ToothIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])},Cog8ToothIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 0 1 1.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.559.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.894.149c-.424.07-.764.383-.929.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 0 1-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.398.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 0 1-.12-1.45l.527-.737c.25-.35.272-.806.108-1.204-.165-.397-.506-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.108-1.204l-.526-.738a1.125 1.125 0 0 1 .12-1.45l.773-.773a1.125 1.125 0 0 1 1.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])},CogIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"})])},CommandLineIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"})])},ComputerDesktopIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25"})])},CpuChipIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"})])},CreditCardIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])},CubeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"})])},CubeTransparentIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m21 7.5-2.25-1.313M21 7.5v2.25m0-2.25-2.25 1.313M3 7.5l2.25-1.313M3 7.5l2.25 1.313M3 7.5v2.25m9 3 2.25-1.313M12 12.75l-2.25-1.313M12 12.75V15m0 6.75 2.25-1.313M12 21.75V19.5m0 2.25-2.25-1.313m0-16.875L12 2.25l2.25 1.313M21 14.25v2.25l-2.25 1.313m-13.5 0L3 16.5v-2.25"})])},CurrencyBangladeshiIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m8.25 7.5.415-.207a.75.75 0 0 1 1.085.67V10.5m0 0h6m-6 0h-1.5m1.5 0v5.438c0 .354.161.697.473.865a3.751 3.751 0 0 0 5.452-2.553c.083-.409-.263-.75-.68-.75h-.745M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},CurrencyDollarIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},CurrencyEuroIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.25 7.756a4.5 4.5 0 1 0 0 8.488M7.5 10.5h5.25m-5.25 3h5.25M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},CurrencyPoundIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.121 7.629A3 3 0 0 0 9.017 9.43c-.023.212-.002.425.028.636l.506 3.541a4.5 4.5 0 0 1-.43 2.65L9 16.5l1.539-.513a2.25 2.25 0 0 1 1.422 0l.655.218a2.25 2.25 0 0 0 1.718-.122L15 15.75M8.25 12H12m9 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},CurrencyRupeeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 8.25H9m6 3H9m3 6-3-3h1.5a3 3 0 1 0 0-6M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},CurrencyYenIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9 7.5 3 4.5m0 0 3-4.5M12 12v5.25M15 12H9m6 3H9m12-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},CursorArrowRaysIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672ZM12 2.25V4.5m5.834.166-1.591 1.591M20.25 10.5H18M7.757 14.743l-1.59 1.59M6 10.5H3.75m4.007-4.243-1.59-1.59"})])},CursorArrowRippleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5"})])},DevicePhoneMobileIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"})])},DeviceTabletIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 19.5h3m-6.75 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-15a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 4.5v15a2.25 2.25 0 0 0 2.25 2.25Z"})])},DivideIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.499 11.998h15m-7.5-6.75h.008v.008h-.008v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM12 18.751h.007v.007H12v-.007Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"})])},DocumentArrowDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentArrowUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 12-3-3m0 0-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentChartBarIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25M9 16.5v.75m3-3v3M15 12v5.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentCheckIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.125 2.25h-4.5c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125v-9M10.125 2.25h.375a9 9 0 0 1 9 9v.375M10.125 2.25A3.375 3.375 0 0 1 13.5 5.625v1.5c0 .621.504 1.125 1.125 1.125h1.5a3.375 3.375 0 0 1 3.375 3.375M9 15l2.25 2.25L15 12"})])},DocumentCurrencyBangladeshiIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 8.25.22-.22a.75.75 0 0 1 1.28.53v6.441c0 .472.214.934.64 1.137a3.75 3.75 0 0 0 4.994-1.77c.205-.428-.152-.868-.627-.868h-.507m-6-2.25h7.5M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentCurrencyDollarIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentCurrencyEuroIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 11.625h4.5m-4.5 2.25h4.5m2.121 1.527c-1.171 1.464-3.07 1.464-4.242 0-1.172-1.465-1.172-3.84 0-5.304 1.171-1.464 3.07-1.464 4.242 0M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentCurrencyPoundIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.621 9.879a3 3 0 0 0-5.02 2.897l.164.609a4.5 4.5 0 0 1-.108 2.676l-.157.439.44-.22a2.863 2.863 0 0 1 2.185-.155c.72.24 1.507.184 2.186-.155L15 18M8.25 15.75H12m-1.5-13.5H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentCurrencyRupeeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 9h3.75m-4.5 2.625h4.5M12 18.75 9.75 16.5h.375a2.625 2.625 0 0 0 0-5.25H9.75m.75-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentCurrencyYenIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m1.5 9 2.25 3m0 0 2.25-3m-2.25 3v4.5M9.75 15h4.5m-4.5 2.25h4.5m-3.75-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentDuplicateIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"})])},DocumentIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentMagnifyingGlassIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"})])},DocumentMinusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 12H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentPlusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v6m3-3H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},DocumentTextIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])},EllipsisHorizontalCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.625 12a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},EllipsisHorizontalIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"})])},EllipsisVerticalIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z"})])},EnvelopeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"})])},EnvelopeOpenIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 9v.906a2.25 2.25 0 0 1-1.183 1.981l-6.478 3.488M2.25 9v.906a2.25 2.25 0 0 0 1.183 1.981l6.478 3.488m8.839 2.51-4.66-2.51m0 0-1.023-.55a2.25 2.25 0 0 0-2.134 0l-1.022.55m0 0-4.661 2.51m16.5 1.615a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V8.844a2.25 2.25 0 0 1 1.183-1.981l7.5-4.039a2.25 2.25 0 0 1 2.134 0l7.5 4.039a2.25 2.25 0 0 1 1.183 1.98V19.5Z"})])},EqualsIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.499 8.248h15m-15 7.501h15"})])},ExclamationCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"})])},ExclamationTriangleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"})])},EyeDropperIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15 11.25 1.5 1.5.75-.75V8.758l2.276-.61a3 3 0 1 0-3.675-3.675l-.61 2.277H12l-.75.75 1.5 1.5M15 11.25l-8.47 8.47c-.34.34-.8.53-1.28.53s-.94.19-1.28.53l-.97.97-.75-.75.97-.97c.34-.34.53-.8.53-1.28s.19-.94.53-1.28L12.75 9M15 11.25 12.75 9"})])},EyeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])},EyeSlashIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"})])},FaceFrownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.182 16.318A4.486 4.486 0 0 0 12.016 15a4.486 4.486 0 0 0-3.198 1.318M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0ZM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Z"})])},FaceSmileIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.182 15.182a4.5 4.5 0 0 1-6.364 0M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0ZM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Z"})])},FilmIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 0 1-1.125-1.125M3.375 19.5h1.5C5.496 19.5 6 18.996 6 18.375m-3.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-1.5A1.125 1.125 0 0 1 18 18.375M20.625 4.5H3.375m17.25 0c.621 0 1.125.504 1.125 1.125M20.625 4.5h-1.5C18.504 4.5 18 5.004 18 5.625m3.75 0v1.5c0 .621-.504 1.125-1.125 1.125M3.375 4.5c-.621 0-1.125.504-1.125 1.125M3.375 4.5h1.5C5.496 4.5 6 5.004 6 5.625m-3.75 0v1.5c0 .621.504 1.125 1.125 1.125m0 0h1.5m-1.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125m1.5-3.75C5.496 8.25 6 7.746 6 7.125v-1.5M4.875 8.25C5.496 8.25 6 8.754 6 9.375v1.5m0-5.25v5.25m0-5.25C6 5.004 6.504 4.5 7.125 4.5h9.75c.621 0 1.125.504 1.125 1.125m1.125 2.625h1.5m-1.5 0A1.125 1.125 0 0 1 18 7.125v-1.5m1.125 2.625c-.621 0-1.125.504-1.125 1.125v1.5m2.625-2.625c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125M18 5.625v5.25M7.125 12h9.75m-9.75 0A1.125 1.125 0 0 1 6 10.875M7.125 12C6.504 12 6 12.504 6 13.125m0-2.25C6 11.496 5.496 12 4.875 12M18 10.875c0 .621-.504 1.125-1.125 1.125M18 10.875c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125m-12 5.25v-5.25m0 5.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125m-12 0v-1.5c0-.621-.504-1.125-1.125-1.125M18 18.375v-5.25m0 5.25v-1.5c0-.621.504-1.125 1.125-1.125M18 13.125v1.5c0 .621.504 1.125 1.125 1.125M18 13.125c0-.621.504-1.125 1.125-1.125M6 13.125v1.5c0 .621-.504 1.125-1.125 1.125M6 13.125C6 12.504 5.496 12 4.875 12m-1.5 0h1.5m-1.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125M19.125 12h1.5m0 0c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h1.5m14.25 0h1.5"})])},FingerPrintIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.864 4.243A7.5 7.5 0 0 1 19.5 10.5c0 2.92-.556 5.709-1.568 8.268M5.742 6.364A7.465 7.465 0 0 0 4.5 10.5a7.464 7.464 0 0 1-1.15 3.993m1.989 3.559A11.209 11.209 0 0 0 8.25 10.5a3.75 3.75 0 1 1 7.5 0c0 .527-.021 1.049-.064 1.565M12 10.5a14.94 14.94 0 0 1-3.6 9.75m6.633-4.596a18.666 18.666 0 0 1-2.485 5.33"})])},FireIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z"})])},FlagIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 3v1.5M3 21v-6m0 0 2.77-.693a9 9 0 0 1 6.208.682l.108.054a9 9 0 0 0 6.086.71l3.114-.732a48.524 48.524 0 0 1-.005-10.499l-3.11.732a9 9 0 0 1-6.085-.711l-.108-.054a9 9 0 0 0-6.208-.682L3 4.5M3 15V4.5"})])},FolderArrowDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9 13.5 3 3m0 0 3-3m-3 3v-6m1.06-4.19-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"})])},FolderIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"})])},FolderMinusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 13.5H9m4.06-7.19-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"})])},FolderOpenIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 0 0-1.883 2.542l.857 6a2.25 2.25 0 0 0 2.227 1.932H19.05a2.25 2.25 0 0 0 2.227-1.932l.857-6a2.25 2.25 0 0 0-1.883-2.542m-16.5 0V6A2.25 2.25 0 0 1 6 3.75h3.879a1.5 1.5 0 0 1 1.06.44l2.122 2.12a1.5 1.5 0 0 0 1.06.44H18A2.25 2.25 0 0 1 20.25 9v.776"})])},FolderPlusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 10.5v6m3-3H9m4.06-7.19-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"})])},ForwardIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 8.689c0-.864.933-1.406 1.683-.977l7.108 4.061a1.125 1.125 0 0 1 0 1.954l-7.108 4.061A1.125 1.125 0 0 1 3 16.811V8.69ZM12.75 8.689c0-.864.933-1.406 1.683-.977l7.108 4.061a1.125 1.125 0 0 1 0 1.954l-7.108 4.061a1.125 1.125 0 0 1-1.683-.977V8.69Z"})])},FunnelIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"})])},GifIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12.75 8.25v7.5m6-7.5h-3V12m0 0v3.75m0-3.75H18M9.75 9.348c-1.03-1.464-2.698-1.464-3.728 0-1.03 1.465-1.03 3.84 0 5.304 1.03 1.464 2.699 1.464 3.728 0V12h-1.5M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])},GiftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])},GiftTopIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 3.75v16.5M2.25 12h19.5M6.375 17.25a4.875 4.875 0 0 0 4.875-4.875V12m6.375 5.25a4.875 4.875 0 0 1-4.875-4.875V12m-9 8.25h16.5a1.5 1.5 0 0 0 1.5-1.5V5.25a1.5 1.5 0 0 0-1.5-1.5H3.75a1.5 1.5 0 0 0-1.5 1.5v13.5a1.5 1.5 0 0 0 1.5 1.5Zm12.621-9.44c-1.409 1.41-4.242 1.061-4.242 1.061s-.349-2.833 1.06-4.242a2.25 2.25 0 0 1 3.182 3.182ZM10.773 7.63c1.409 1.409 1.06 4.242 1.06 4.242S9 12.22 7.592 10.811a2.25 2.25 0 1 1 3.182-3.182Z"})])},GlobeAltIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"})])},GlobeAmericasIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m6.115 5.19.319 1.913A6 6 0 0 0 8.11 10.36L9.75 12l-.387.775c-.217.433-.132.956.21 1.298l1.348 1.348c.21.21.329.497.329.795v1.089c0 .426.24.815.622 1.006l.153.076c.433.217.956.132 1.298-.21l.723-.723a8.7 8.7 0 0 0 2.288-4.042 1.087 1.087 0 0 0-.358-1.099l-1.33-1.108c-.251-.21-.582-.299-.905-.245l-1.17.195a1.125 1.125 0 0 1-.98-.314l-.295-.295a1.125 1.125 0 0 1 0-1.591l.13-.132a1.125 1.125 0 0 1 1.3-.21l.603.302a.809.809 0 0 0 1.086-1.086L14.25 7.5l1.256-.837a4.5 4.5 0 0 0 1.528-1.732l.146-.292M6.115 5.19A9 9 0 1 0 17.18 4.64M6.115 5.19A8.965 8.965 0 0 1 12 3c1.929 0 3.716.607 5.18 1.64"})])},GlobeAsiaAustraliaIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12.75 3.03v.568c0 .334.148.65.405.864l1.068.89c.442.369.535 1.01.216 1.49l-.51.766a2.25 2.25 0 0 1-1.161.886l-.143.048a1.107 1.107 0 0 0-.57 1.664c.369.555.169 1.307-.427 1.605L9 13.125l.423 1.059a.956.956 0 0 1-1.652.928l-.679-.906a1.125 1.125 0 0 0-1.906.172L4.5 15.75l-.612.153M12.75 3.031a9 9 0 0 0-8.862 12.872M12.75 3.031a9 9 0 0 1 6.69 14.036m0 0-.177-.529A2.25 2.25 0 0 0 17.128 15H16.5l-.324-.324a1.453 1.453 0 0 0-2.328.377l-.036.073a1.586 1.586 0 0 1-.982.816l-.99.282c-.55.157-.894.702-.8 1.267l.073.438c.08.474.49.821.97.821.846 0 1.598.542 1.865 1.345l.215.643m5.276-3.67a9.012 9.012 0 0 1-5.276 3.67m0 0a9 9 0 0 1-10.275-4.835M15.75 9c0 .896-.393 1.7-1.016 2.25"})])},GlobeEuropeAfricaIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m20.893 13.393-1.135-1.135a2.252 2.252 0 0 1-.421-.585l-1.08-2.16a.414.414 0 0 0-.663-.107.827.827 0 0 1-.812.21l-1.273-.363a.89.89 0 0 0-.738 1.595l.587.39c.59.395.674 1.23.172 1.732l-.2.2c-.212.212-.33.498-.33.796v.41c0 .409-.11.809-.32 1.158l-1.315 2.191a2.11 2.11 0 0 1-1.81 1.025 1.055 1.055 0 0 1-1.055-1.055v-1.172c0-.92-.56-1.747-1.414-2.089l-.655-.261a2.25 2.25 0 0 1-1.383-2.46l.007-.042a2.25 2.25 0 0 1 .29-.787l.09-.15a2.25 2.25 0 0 1 2.37-1.048l1.178.236a1.125 1.125 0 0 0 1.302-.795l.208-.73a1.125 1.125 0 0 0-.578-1.315l-.665-.332-.091.091a2.25 2.25 0 0 1-1.591.659h-.18c-.249 0-.487.1-.662.274a.931.931 0 0 1-1.458-1.137l1.411-2.353a2.25 2.25 0 0 0 .286-.76m11.928 9.869A9 9 0 0 0 8.965 3.525m11.928 9.868A9 9 0 1 1 8.965 3.525"})])},H1Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.243 4.493v7.5m0 0v7.502m0-7.501h10.5m0-7.5v7.5m0 0v7.501m4.501-8.627 2.25-1.5v10.126m0 0h-2.25m2.25 0h2.25"})])},H2Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 19.5H16.5v-1.609a2.25 2.25 0 0 1 1.244-2.012l2.89-1.445c.651-.326 1.116-.955 1.116-1.683 0-.498-.04-.987-.118-1.463-.135-.825-.835-1.422-1.668-1.489a15.202 15.202 0 0 0-3.464.12M2.243 4.492v7.5m0 0v7.502m0-7.501h10.5m0-7.5v7.5m0 0v7.501"})])},H3Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.905 14.626a4.52 4.52 0 0 1 .738 3.603c-.154.695-.794 1.143-1.504 1.208a15.194 15.194 0 0 1-3.639-.104m4.405-4.707a4.52 4.52 0 0 0 .738-3.603c-.154-.696-.794-1.144-1.504-1.209a15.19 15.19 0 0 0-3.639.104m4.405 4.708H18M2.243 4.493v7.5m0 0v7.502m0-7.501h10.5m0-7.5v7.5m0 0v7.501"})])},HandRaisedIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.05 4.575a1.575 1.575 0 1 0-3.15 0v3m3.15-3v-1.5a1.575 1.575 0 0 1 3.15 0v1.5m-3.15 0 .075 5.925m3.075.75V4.575m0 0a1.575 1.575 0 0 1 3.15 0V15M6.9 7.575a1.575 1.575 0 1 0-3.15 0v8.175a6.75 6.75 0 0 0 6.75 6.75h2.018a5.25 5.25 0 0 0 3.712-1.538l1.732-1.732a5.25 5.25 0 0 0 1.538-3.712l.003-2.024a.668.668 0 0 1 .198-.471 1.575 1.575 0 1 0-2.228-2.228 3.818 3.818 0 0 0-1.12 2.687M6.9 7.575V12m6.27 4.318A4.49 4.49 0 0 1 16.35 15m.002 0h-.002"})])},HandThumbDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.498 15.25H4.372c-1.026 0-1.945-.694-2.054-1.715a12.137 12.137 0 0 1-.068-1.285c0-2.848.992-5.464 2.649-7.521C5.287 4.247 5.886 4 6.504 4h4.016a4.5 4.5 0 0 1 1.423.23l3.114 1.04a4.5 4.5 0 0 0 1.423.23h1.294M7.498 15.25c.618 0 .991.724.725 1.282A7.471 7.471 0 0 0 7.5 19.75 2.25 2.25 0 0 0 9.75 22a.75.75 0 0 0 .75-.75v-.633c0-.573.11-1.14.322-1.672.304-.76.93-1.33 1.653-1.715a9.04 9.04 0 0 0 2.86-2.4c.498-.634 1.226-1.08 2.032-1.08h.384m-10.253 1.5H9.7m8.075-9.75c.01.05.027.1.05.148.593 1.2.925 2.55.925 3.977 0 1.487-.36 2.89-.999 4.125m.023-8.25c-.076-.365.183-.75.575-.75h.908c.889 0 1.713.518 1.972 1.368.339 1.11.521 2.287.521 3.507 0 1.553-.295 3.036-.831 4.398-.306.774-1.086 1.227-1.918 1.227h-1.053c-.472 0-.745-.556-.5-.96a8.95 8.95 0 0 0 .303-.54"})])},HandThumbUpIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.633 10.25c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 0 1 2.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 0 0 .322-1.672V2.75a.75.75 0 0 1 .75-.75 2.25 2.25 0 0 1 2.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282m0 0h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 0 1-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 0 0-1.423-.23H5.904m10.598-9.75H14.25M5.904 18.5c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 0 1-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 9.953 4.167 9.5 5 9.5h1.053c.472 0 .745.556.5.96a8.958 8.958 0 0 0-1.302 4.665c0 1.194.232 2.333.654 3.375Z"})])},HashtagIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 8.25h15m-16.5 7.5h15m-1.8-13.5-3.9 19.5m-2.1-19.5-3.9 19.5"})])},HeartIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"})])},HomeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"})])},HomeModernIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 21v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21m0 0h4.5V3.545M12.75 21h7.5V10.75M2.25 21h1.5m18 0h-18M2.25 9l4.5-1.636M18.75 3l-1.5.545m0 6.205 3 1m1.5.5-1.5-.5M6.75 7.364V3h-3v18m3-13.636 10.5-3.819"})])},IdentificationIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z"})])},InboxArrowDownIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 3.75H6.912a2.25 2.25 0 0 0-2.15 1.588L2.35 13.177a2.25 2.25 0 0 0-.1.661V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18v-4.162c0-.224-.034-.447-.1-.661L19.24 5.338a2.25 2.25 0 0 0-2.15-1.588H15M2.25 13.5h3.86a2.25 2.25 0 0 1 2.012 1.244l.256.512a2.25 2.25 0 0 0 2.013 1.244h3.218a2.25 2.25 0 0 0 2.013-1.244l.256-.512a2.25 2.25 0 0 1 2.013-1.244h3.859M12 3v8.25m0 0-3-3m3 3 3-3"})])},InboxIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 13.5h3.86a2.25 2.25 0 0 1 2.012 1.244l.256.512a2.25 2.25 0 0 0 2.013 1.244h3.218a2.25 2.25 0 0 0 2.013-1.244l.256-.512a2.25 2.25 0 0 1 2.013-1.244h3.859m-19.5.338V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18v-4.162c0-.224-.034-.447-.1-.661L19.24 5.338a2.25 2.25 0 0 0-2.15-1.588H6.911a2.25 2.25 0 0 0-2.15 1.588L2.35 13.177a2.25 2.25 0 0 0-.1.661Z"})])},InboxStackIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m7.875 14.25 1.214 1.942a2.25 2.25 0 0 0 1.908 1.058h2.006c.776 0 1.497-.4 1.908-1.058l1.214-1.942M2.41 9h4.636a2.25 2.25 0 0 1 1.872 1.002l.164.246a2.25 2.25 0 0 0 1.872 1.002h2.092a2.25 2.25 0 0 0 1.872-1.002l.164-.246A2.25 2.25 0 0 1 16.954 9h4.636M2.41 9a2.25 2.25 0 0 0-.16.832V12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 12V9.832c0-.287-.055-.57-.16-.832M2.41 9a2.25 2.25 0 0 1 .382-.632l3.285-3.832a2.25 2.25 0 0 1 1.708-.786h8.43c.657 0 1.281.287 1.709.786l3.284 3.832c.163.19.291.404.382.632M4.5 20.25h15A2.25 2.25 0 0 0 21.75 18v-2.625c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125V18a2.25 2.25 0 0 0 2.25 2.25Z"})])},InformationCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"})])},ItalicIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.248 20.246H9.05m0 0h3.696m-3.696 0 5.893-16.502m0 0h-3.697m3.697 0h3.803"})])},KeyIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"})])},LanguageIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802"})])},LifebuoyIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.712 4.33a9.027 9.027 0 0 1 1.652 1.306c.51.51.944 1.064 1.306 1.652M16.712 4.33l-3.448 4.138m3.448-4.138a9.014 9.014 0 0 0-9.424 0M19.67 7.288l-4.138 3.448m4.138-3.448a9.014 9.014 0 0 1 0 9.424m-4.138-5.976a3.736 3.736 0 0 0-.88-1.388 3.737 3.737 0 0 0-1.388-.88m2.268 2.268a3.765 3.765 0 0 1 0 2.528m-2.268-4.796a3.765 3.765 0 0 0-2.528 0m4.796 4.796c-.181.506-.475.982-.88 1.388a3.736 3.736 0 0 1-1.388.88m2.268-2.268 4.138 3.448m0 0a9.027 9.027 0 0 1-1.306 1.652c-.51.51-1.064.944-1.652 1.306m0 0-3.448-4.138m3.448 4.138a9.014 9.014 0 0 1-9.424 0m5.976-4.138a3.765 3.765 0 0 1-2.528 0m0 0a3.736 3.736 0 0 1-1.388-.88 3.737 3.737 0 0 1-.88-1.388m2.268 2.268L7.288 19.67m0 0a9.024 9.024 0 0 1-1.652-1.306 9.027 9.027 0 0 1-1.306-1.652m0 0 4.138-3.448M4.33 16.712a9.014 9.014 0 0 1 0-9.424m4.138 5.976a3.765 3.765 0 0 1 0-2.528m0 0c.181-.506.475-.982.88-1.388a3.736 3.736 0 0 1 1.388-.88m-2.268 2.268L4.33 7.288m6.406 1.18L7.288 4.33m0 0a9.024 9.024 0 0 0-1.652 1.306A9.025 9.025 0 0 0 4.33 7.288"})])},LightBulbIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"})])},LinkIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"})])},LinkSlashIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.181 8.68a4.503 4.503 0 0 1 1.903 6.405m-9.768-2.782L3.56 14.06a4.5 4.5 0 0 0 6.364 6.365l3.129-3.129m5.614-5.615 1.757-1.757a4.5 4.5 0 0 0-6.364-6.365l-4.5 4.5c-.258.26-.479.541-.661.84m1.903 6.405a4.495 4.495 0 0 1-1.242-.88 4.483 4.483 0 0 1-1.062-1.683m6.587 2.345 5.907 5.907m-5.907-5.907L8.898 8.898M2.991 2.99 8.898 8.9"})])},ListBulletIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"})])},LockClosedIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"})])},LockOpenIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 10.5V6.75a4.5 4.5 0 1 1 9 0v3.75M3.75 21.75h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H3.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"})])},MagnifyingGlassCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15.75 15.75-2.489-2.489m0 0a3.375 3.375 0 1 0-4.773-4.773 3.375 3.375 0 0 0 4.774 4.774ZM21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},MagnifyingGlassIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"})])},MagnifyingGlassMinusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607ZM13.5 10.5h-6"})])},MagnifyingGlassPlusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607ZM10.5 7.5v6m3-3h-6"})])},MapIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 6.75V15m6-6v8.25m.503 3.498 4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 0 0-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0Z"})])},MapPinIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"})])},MegaphoneIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"})])},MicrophoneIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z"})])},MinusCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},MinusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5 12h14"})])},MinusSmallIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 12H6"})])},MoonIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"})])},MusicalNoteIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9 9 10.5-3m0 6.553v3.75a2.25 2.25 0 0 1-1.632 2.163l-1.32.377a1.803 1.803 0 1 1-.99-3.467l2.31-.66a2.25 2.25 0 0 0 1.632-2.163Zm0 0V2.25L9 5.25v10.303m0 0v3.75a2.25 2.25 0 0 1-1.632 2.163l-1.32.377a1.803 1.803 0 0 1-.99-3.467l2.31-.66A2.25 2.25 0 0 0 9 15.553Z"})])},NewspaperIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z"})])},NoSymbolIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"})])},NumberedListIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.242 5.992h12m-12 6.003H20.24m-12 5.999h12M4.117 7.495v-3.75H2.99m1.125 3.75H2.99m1.125 0H5.24m-1.92 2.577a1.125 1.125 0 1 1 1.591 1.59l-1.83 1.83h2.16M2.99 15.745h1.125a1.125 1.125 0 0 1 0 2.25H3.74m0-.002h.375a1.125 1.125 0 0 1 0 2.25H2.99"})])},PaintBrushIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.53 16.122a3 3 0 0 0-5.78 1.128 2.25 2.25 0 0 1-2.4 2.245 4.5 4.5 0 0 0 8.4-2.245c0-.399-.078-.78-.22-1.128Zm0 0a15.998 15.998 0 0 0 3.388-1.62m-5.043-.025a15.994 15.994 0 0 1 1.622-3.395m3.42 3.42a15.995 15.995 0 0 0 4.764-4.648l3.876-5.814a1.151 1.151 0 0 0-1.597-1.597L14.146 6.32a15.996 15.996 0 0 0-4.649 4.763m3.42 3.42a6.776 6.776 0 0 0-3.42-3.42"})])},PaperAirplaneIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5"})])},PaperClipIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m18.375 12.739-7.693 7.693a4.5 4.5 0 0 1-6.364-6.364l10.94-10.94A3 3 0 1 1 19.5 7.372L8.552 18.32m.009-.01-.01.01m5.699-9.941-7.81 7.81a1.5 1.5 0 0 0 2.112 2.13"})])},PauseCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.25 9v6m-4.5 0V9M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},PauseIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 5.25v13.5m-7.5-13.5v13.5"})])},PencilIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"})])},PencilSquareIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"})])},PercentBadgeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m8.99 14.993 6-6m6 3.001c0 1.268-.63 2.39-1.593 3.069a3.746 3.746 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043 3.745 3.745 0 0 1-3.068 1.593c-1.268 0-2.39-.63-3.068-1.593a3.745 3.745 0 0 1-3.296-1.043 3.746 3.746 0 0 1-1.043-3.297 3.746 3.746 0 0 1-1.593-3.068c0-1.268.63-2.39 1.593-3.068a3.746 3.746 0 0 1 1.043-3.297 3.745 3.745 0 0 1 3.296-1.042 3.745 3.745 0 0 1 3.068-1.594c1.268 0 2.39.63 3.068 1.593a3.745 3.745 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.297 3.746 3.746 0 0 1 1.593 3.068ZM9.74 9.743h.008v.007H9.74v-.007Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm4.125 4.5h.008v.008h-.008v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"})])},PhoneArrowDownLeftIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.25 9.75v-4.5m0 4.5h4.5m-4.5 0 6-6m-3 18c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 0 1 4.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 0 0-.38 1.21 12.035 12.035 0 0 0 7.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 0 1 1.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 0 1-2.25 2.25h-2.25Z"})])},PhoneArrowUpRightIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.25 3.75v4.5m0-4.5h-4.5m4.5 0-6 6m3 12c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 0 1 4.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 0 0-.38 1.21 12.035 12.035 0 0 0 7.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 0 1 1.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 0 1-2.25 2.25h-2.25Z"})])},PhoneIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"})])},PhoneXMarkIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 3.75 18 6m0 0 2.25 2.25M18 6l2.25-2.25M18 6l-2.25 2.25m1.5 13.5c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 0 1 4.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 0 0-.38 1.21 12.035 12.035 0 0 0 7.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293a1.125 1.125 0 0 1 1.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 0 1-2.25 2.25h-2.25Z"})])},PhotoIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"})])},PlayCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.91 11.672a.375.375 0 0 1 0 .656l-5.603 3.113a.375.375 0 0 1-.557-.328V8.887c0-.286.307-.466.557-.327l5.603 3.112Z"})])},PlayIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"})])},PlayPauseIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 7.5V18M15 7.5V18M3 16.811V8.69c0-.864.933-1.406 1.683-.977l7.108 4.061a1.125 1.125 0 0 1 0 1.954l-7.108 4.061A1.125 1.125 0 0 1 3 16.811Z"})])},PlusCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},PlusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])},PlusSmallIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v12m6-6H6"})])},PowerIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.636 5.636a9 9 0 1 0 12.728 0M12 3v9"})])},PresentationChartBarIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6"})])},PresentationChartLineIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5m.75-9 3-3 2.148 2.148A12.061 12.061 0 0 1 16.5 7.605"})])},PrinterIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"})])},PuzzlePieceIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.25 6.087c0-.355.186-.676.401-.959.221-.29.349-.634.349-1.003 0-1.036-1.007-1.875-2.25-1.875s-2.25.84-2.25 1.875c0 .369.128.713.349 1.003.215.283.401.604.401.959v0a.64.64 0 0 1-.657.643 48.39 48.39 0 0 1-4.163-.3c.186 1.613.293 3.25.315 4.907a.656.656 0 0 1-.658.663v0c-.355 0-.676-.186-.959-.401a1.647 1.647 0 0 0-1.003-.349c-1.036 0-1.875 1.007-1.875 2.25s.84 2.25 1.875 2.25c.369 0 .713-.128 1.003-.349.283-.215.604-.401.959-.401v0c.31 0 .555.26.532.57a48.039 48.039 0 0 1-.642 5.056c1.518.19 3.058.309 4.616.354a.64.64 0 0 0 .657-.643v0c0-.355-.186-.676-.401-.959a1.647 1.647 0 0 1-.349-1.003c0-1.035 1.008-1.875 2.25-1.875 1.243 0 2.25.84 2.25 1.875 0 .369-.128.713-.349 1.003-.215.283-.4.604-.4.959v0c0 .333.277.599.61.58a48.1 48.1 0 0 0 5.427-.63 48.05 48.05 0 0 0 .582-4.717.532.532 0 0 0-.533-.57v0c-.355 0-.676.186-.959.401-.29.221-.634.349-1.003.349-1.035 0-1.875-1.007-1.875-2.25s.84-2.25 1.875-2.25c.37 0 .713.128 1.003.349.283.215.604.401.96.401v0a.656.656 0 0 0 .658-.663 48.422 48.422 0 0 0-.37-5.36c-1.886.342-3.81.574-5.766.689a.578.578 0 0 1-.61-.58v0Z"})])},QrCodeIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0 1 3.75 9.375v-4.5ZM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 0 1-1.125-1.125v-4.5ZM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0 1 13.5 9.375v-4.5Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 6.75h.75v.75h-.75v-.75ZM6.75 16.5h.75v.75h-.75v-.75ZM16.5 6.75h.75v.75h-.75v-.75ZM13.5 13.5h.75v.75h-.75v-.75ZM13.5 19.5h.75v.75h-.75v-.75ZM19.5 13.5h.75v.75h-.75v-.75ZM19.5 19.5h.75v.75h-.75v-.75ZM16.5 16.5h.75v.75h-.75v-.75Z"})])},QuestionMarkCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z"})])},QueueListIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z"})])},RadioIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m3.75 7.5 16.5-4.125M12 6.75c-2.708 0-5.363.224-7.948.655C2.999 7.58 2.25 8.507 2.25 9.574v9.176A2.25 2.25 0 0 0 4.5 21h15a2.25 2.25 0 0 0 2.25-2.25V9.574c0-1.067-.75-1.994-1.802-2.169A48.329 48.329 0 0 0 12 6.75Zm-1.683 6.443-.005.005-.006-.005.006-.005.005.005Zm-.005 2.127-.005-.006.005-.005.005.005-.005.005Zm-2.116-.006-.005.006-.006-.006.005-.005.006.005Zm-.005-2.116-.006-.005.006-.005.005.005-.005.005ZM9.255 10.5v.008h-.008V10.5h.008Zm3.249 1.88-.007.004-.003-.007.006-.003.004.006Zm-1.38 5.126-.003-.006.006-.004.004.007-.006.003Zm.007-6.501-.003.006-.007-.003.004-.007.006.004Zm1.37 5.129-.007-.004.004-.006.006.003-.004.007Zm.504-1.877h-.008v-.007h.008v.007ZM9.255 18v.008h-.008V18h.008Zm-3.246-1.87-.007.004L6 16.127l.006-.003.004.006Zm1.366-5.119-.004-.006.006-.004.004.007-.006.003ZM7.38 17.5l-.003.006-.007-.003.004-.007.006.004Zm-1.376-5.116L6 12.38l.003-.007.007.004-.004.007Zm-.5 1.873h-.008v-.007h.008v.007ZM17.25 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Zm0 4.5a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z"})])},ReceiptPercentIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9 14.25 6-6m4.5-3.493V21.75l-3.75-1.5-3.75 1.5-3.75-1.5-3.75 1.5V4.757c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0c1.1.128 1.907 1.077 1.907 2.185ZM9.75 9h.008v.008H9.75V9Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm4.125 4.5h.008v.008h-.008V13.5Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"})])},ReceiptRefundIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 9.75h4.875a2.625 2.625 0 0 1 0 5.25H12M8.25 9.75 10.5 7.5M8.25 9.75 10.5 12m9-7.243V21.75l-3.75-1.5-3.75 1.5-3.75-1.5-3.75 1.5V4.757c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0c1.1.128 1.907 1.077 1.907 2.185Z"})])},RectangleGroupIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 0 1-1.125-1.125v-3.75ZM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-8.25ZM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-2.25Z"})])},RectangleStackIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 6.878V6a2.25 2.25 0 0 1 2.25-2.25h7.5A2.25 2.25 0 0 1 18 6v.878m-12 0c.235-.083.487-.128.75-.128h10.5c.263 0 .515.045.75.128m-12 0A2.25 2.25 0 0 0 4.5 9v.878m13.5-3A2.25 2.25 0 0 1 19.5 9v.878m0 0a2.246 2.246 0 0 0-.75-.128H5.25c-.263 0-.515.045-.75.128m15 0A2.25 2.25 0 0 1 21 12v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6c0-.98.626-1.813 1.5-2.122"})])},RocketLaunchIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"})])},RssIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12.75 19.5v-.75a7.5 7.5 0 0 0-7.5-7.5H4.5m0-6.75h.75c7.87 0 14.25 6.38 14.25 14.25v.75M6 18.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"})])},ScaleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 3v17.25m0 0c-1.472 0-2.882.265-4.185.75M12 20.25c1.472 0 2.882.265 4.185.75M18.75 4.97A48.416 48.416 0 0 0 12 4.5c-2.291 0-4.545.16-6.75.47m13.5 0c1.01.143 2.01.317 3 .52m-3-.52 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.988 5.988 0 0 1-2.031.352 5.988 5.988 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L18.75 4.971Zm-16.5.52c.99-.203 1.99-.377 3-.52m0 0 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.989 5.989 0 0 1-2.031.352 5.989 5.989 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L5.25 4.971Z"})])},ScissorsIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m7.848 8.25 1.536.887M7.848 8.25a3 3 0 1 1-5.196-3 3 3 0 0 1 5.196 3Zm1.536.887a2.165 2.165 0 0 1 1.083 1.839c.005.351.054.695.14 1.024M9.384 9.137l2.077 1.199M7.848 15.75l1.536-.887m-1.536.887a3 3 0 1 1-5.196 3 3 3 0 0 1 5.196-3Zm1.536-.887a2.165 2.165 0 0 0 1.083-1.838c.005-.352.054-.695.14-1.025m-1.223 2.863 2.077-1.199m0-3.328a4.323 4.323 0 0 1 2.068-1.379l5.325-1.628a4.5 4.5 0 0 1 2.48-.044l.803.215-7.794 4.5m-2.882-1.664A4.33 4.33 0 0 0 10.607 12m3.736 0 7.794 4.5-.802.215a4.5 4.5 0 0 1-2.48-.043l-5.326-1.629a4.324 4.324 0 0 1-2.068-1.379M14.343 12l-2.882 1.664"})])},ServerIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"})])},ServerStackIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 14.25h13.5m-13.5 0a3 3 0 0 1-3-3m3 3a3 3 0 1 0 0 6h13.5a3 3 0 1 0 0-6m-16.5-3a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3m-19.5 0a4.5 4.5 0 0 1 .9-2.7L5.737 5.1a3.375 3.375 0 0 1 2.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l2.587 3.45a4.5 4.5 0 0 1 .9 2.7m0 0a3 3 0 0 1-3 3m0 3h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Zm-3 6h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Z"})])},ShareIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"})])},ShieldCheckIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"})])},ShieldExclamationIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m0-10.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.75c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.25-8.25-3.286Zm0 13.036h.008v.008H12v-.008Z"})])},ShoppingBagIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"})])},ShoppingCartIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"})])},SignalIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"})])},SignalSlashIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m3 3 8.735 8.735m0 0a.374.374 0 1 1 .53.53m-.53-.53.53.53m0 0L21 21M14.652 9.348a3.75 3.75 0 0 1 0 5.304m2.121-7.425a6.75 6.75 0 0 1 0 9.546m2.121-11.667c3.808 3.807 3.808 9.98 0 13.788m-9.546-4.242a3.733 3.733 0 0 1-1.06-2.122m-1.061 4.243a6.75 6.75 0 0 1-1.625-6.929m-.496 9.05c-3.068-3.067-3.664-7.67-1.79-11.334M12 12h.008v.008H12V12Z"})])},SlashIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9 20.247 6-16.5"})])},SparklesIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"})])},SpeakerWaveIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"})])},SpeakerXMarkIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.25 9.75 19.5 12m0 0 2.25 2.25M19.5 12l2.25-2.25M19.5 12l-2.25 2.25m-10.5-6 4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"})])},Square2StackIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 8.25V6a2.25 2.25 0 0 0-2.25-2.25H6A2.25 2.25 0 0 0 3.75 6v8.25A2.25 2.25 0 0 0 6 16.5h2.25m8.25-8.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-7.5A2.25 2.25 0 0 1 8.25 18v-1.5m8.25-8.25h-6a2.25 2.25 0 0 0-2.25 2.25v6"})])},Square3Stack3DIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3"})])},Squares2X2Icon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"})])},SquaresPlusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 16.875h3.375m0 0h3.375m-3.375 0V13.5m0 3.375v3.375M6 10.5h2.25a2.25 2.25 0 0 0 2.25-2.25V6a2.25 2.25 0 0 0-2.25-2.25H6A2.25 2.25 0 0 0 3.75 6v2.25A2.25 2.25 0 0 0 6 10.5Zm0 9.75h2.25A2.25 2.25 0 0 0 10.5 18v-2.25a2.25 2.25 0 0 0-2.25-2.25H6a2.25 2.25 0 0 0-2.25 2.25V18A2.25 2.25 0 0 0 6 20.25Zm9.75-9.75H18a2.25 2.25 0 0 0 2.25-2.25V6A2.25 2.25 0 0 0 18 3.75h-2.25A2.25 2.25 0 0 0 13.5 6v2.25a2.25 2.25 0 0 0 2.25 2.25Z"})])},StarIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"})])},StopCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 9.563C9 9.252 9.252 9 9.563 9h4.874c.311 0 .563.252.563.563v4.874c0 .311-.252.563-.563.563H9.564A.562.562 0 0 1 9 14.437V9.564Z"})])},StopIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z"})])},StrikethroughIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 12a8.912 8.912 0 0 1-.318-.079c-1.585-.424-2.904-1.247-3.76-2.236-.873-1.009-1.265-2.19-.968-3.301.59-2.2 3.663-3.29 6.863-2.432A8.186 8.186 0 0 1 16.5 5.21M6.42 17.81c.857.99 2.176 1.812 3.761 2.237 3.2.858 6.274-.23 6.863-2.431.233-.868.044-1.779-.465-2.617M3.75 12h16.5"})])},SunIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"})])},SwatchIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.098 19.902a3.75 3.75 0 0 0 5.304 0l6.401-6.402M6.75 21A3.75 3.75 0 0 1 3 17.25V4.125C3 3.504 3.504 3 4.125 3h5.25c.621 0 1.125.504 1.125 1.125v4.072M6.75 21a3.75 3.75 0 0 0 3.75-3.75V8.197M6.75 21h13.125c.621 0 1.125-.504 1.125-1.125v-5.25c0-.621-.504-1.125-1.125-1.125h-4.072M10.5 8.197l2.88-2.88c.438-.439 1.15-.439 1.59 0l3.712 3.713c.44.44.44 1.152 0 1.59l-2.879 2.88M6.75 17.25h.008v.008H6.75v-.008Z"})])},TableCellsIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 0 1-1.125-1.125M3.375 19.5h7.5c.621 0 1.125-.504 1.125-1.125m-9.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-7.5A1.125 1.125 0 0 1 12 18.375m9.75-12.75c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125m19.5 0v1.5c0 .621-.504 1.125-1.125 1.125M2.25 5.625v1.5c0 .621.504 1.125 1.125 1.125m0 0h17.25m-17.25 0h7.5c.621 0 1.125.504 1.125 1.125M3.375 8.25c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125m17.25-3.75h-7.5c-.621 0-1.125.504-1.125 1.125m8.625-1.125c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h7.5m-7.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125M12 10.875v-1.5m0 1.5c0 .621-.504 1.125-1.125 1.125M12 10.875c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125M13.125 12h7.5m-7.5 0c-.621 0-1.125.504-1.125 1.125M20.625 12c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h7.5M12 14.625v-1.5m0 1.5c0 .621-.504 1.125-1.125 1.125M12 14.625c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125m0 1.5v-1.5m0 0c0-.621.504-1.125 1.125-1.125m0 0h7.5"})])},TagIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 6h.008v.008H6V6Z"})])},TicketIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z"})])},TrashIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"})])},TrophyIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"})])},TruckIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"})])},TvIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 20.25h12m-7.5-3v3m3-3v3m-10.125-3h17.25c.621 0 1.125-.504 1.125-1.125V4.875c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125Z"})])},UnderlineIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.995 3.744v7.5a6 6 0 1 1-12 0v-7.5m-2.25 16.502h16.5"})])},UserCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])},UserGroupIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"})])},UserIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])},UserMinusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M22 10.5h-6m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM4 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 10.374 21c-2.331 0-4.512-.645-6.374-1.766Z"})])},UserPlusIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"})])},UsersIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"})])},VariableIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.745 3A23.933 23.933 0 0 0 3 12c0 3.183.62 6.22 1.745 9M19.5 3c.967 2.78 1.5 5.817 1.5 9s-.533 6.22-1.5 9M8.25 8.885l1.444-.89a.75.75 0 0 1 1.105.402l2.402 7.206a.75.75 0 0 0 1.104.401l1.445-.889m-8.25.75.213.09a1.687 1.687 0 0 0 2.062-.617l4.45-6.676a1.688 1.688 0 0 1 2.062-.618l.213.09"})])},VideoCameraIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z"})])},VideoCameraSlashIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M12 18.75H4.5a2.25 2.25 0 0 1-2.25-2.25V9m12.841 9.091L16.5 19.5m-1.409-1.409c.407-.407.659-.97.659-1.591v-9a2.25 2.25 0 0 0-2.25-2.25h-9c-.621 0-1.184.252-1.591.659m12.182 12.182L2.909 5.909M1.5 4.5l1.409 1.409"})])},ViewColumnsIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v12.75c0 .621.504 1.125 1.125 1.125Z"})])},ViewfinderCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.5 3.75H6A2.25 2.25 0 0 0 3.75 6v1.5M16.5 3.75H18A2.25 2.25 0 0 1 20.25 6v1.5m0 9V18A2.25 2.25 0 0 1 18 20.25h-1.5m-9 0H6A2.25 2.25 0 0 1 3.75 18v-1.5M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])},WalletIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 12a2.25 2.25 0 0 0-2.25-2.25H15a3 3 0 1 1-6 0H5.25A2.25 2.25 0 0 0 3 12m18 0v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6m18 0V9M3 12V9m18 0a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 9m18 0V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v3"})])},WifiIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.288 15.038a5.25 5.25 0 0 1 7.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 0 1 1.06 0Z"})])},WindowIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 8.25V18a2.25 2.25 0 0 0 2.25 2.25h13.5A2.25 2.25 0 0 0 21 18V8.25m-18 0V6a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 6v2.25m-18 0h18M5.25 6h.008v.008H5.25V6ZM7.5 6h.008v.008H7.5V6Zm2.25 0h.008v.008H9.75V6Z"})])},WrenchIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 6.75a4.5 4.5 0 0 1-4.884 4.484c-1.076-.091-2.264.071-2.95.904l-7.152 8.684a2.548 2.548 0 1 1-3.586-3.586l8.684-7.152c.833-.686.995-1.874.904-2.95a4.5 4.5 0 0 1 6.336-4.486l-3.276 3.276a3.004 3.004 0 0 0 2.25 2.25l3.276-3.276c.256.565.398 1.192.398 1.852Z"}),Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.867 19.125h.008v.008h-.008v-.008Z"})])},WrenchScrewdriverIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z"})])},XCircleIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])},XMarkIcon:function(e,t){return Ie(),Ce("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[Ae("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 18 18 6M6 6l12 12"})])}},Symbol.toStringTag,{value:"Module"}));window.Vue={createApp:ye,createPinia:Be},window.HeadlessUI=Cn,window.HeroIcons=In,window.axios=Se,Se.defaults.xsrfCookieName="csrftoken",Se.defaults.xsrfHeaderName="X-CSRFToken",Se.interceptors.request.use(e=>{var t;const o=null==(t=window.FinderV2Config)?void 0:t.csrfToken;return o&&(e.headers["X-CSRFToken"]=o),e}),console.log("Finder-v2 widget libraries loaded");
