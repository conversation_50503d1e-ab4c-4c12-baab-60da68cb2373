import base64
from urllib.parse import urlparse
import logging

from django.http import Http404, HttpResponse
from rest_framework_proxy.views import ProxyView

from src.apps.widgets.api_proxy.throttling import WidgetProxyDayThrottle, \
    WidgetProxyMinuteThrottle
from src.apps.widgets.translation.constants import API_CHINESE_LANG, \
    WIDGET_CHINESE_LANG_CHOICES


class WsProtectMixin(object):
    """
    CSRF protection mixin for widget API proxy views.

    Provides security validation for widget API requests by checking:
    1. Referer header validation (same-origin policy)
    2. Custom CSRF token validation based on User-Agent
    3. Subscription ban status checking

    This mixin is used by both WidgetProxyView and FinderWidgetProxyView
    to ensure secure API access from widget iframes.
    """

    def dispatch(self, request, *args, **kwargs):
        """
        Override dispatch to add CSRF protection before processing the request.

        Args:
            request: Django HttpRequest object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments

        Returns:
            HttpResponse: Response from parent class if validation passes

        Raises:
            Http404: If CSRF validation fails (security measure to hide API endpoints)
        """
        if not self.is_allowed(request):
            raise Http404

        return super(WsProtectMixin, self).dispatch(request, *args, **kwargs)

    def is_allowed(self, request):
        """
        Validate if the request is allowed based on CSRF protection rules.

        Validation steps:
        1. Check if CSRF checks are disabled (for testing)
        2. Check if user subscription is banned
        3. Validate referer header exists and matches request hostname
        4. Validate custom CSRF token matches expected value

        Args:
            request: Django HttpRequest object

        Returns:
            bool: True if request is allowed, False otherwise
        """
        # Allow requests with CSRF checks disabled (for testing/debugging)
        if getattr(request, '_dont_enforce_csrf_checks', False):
            return True

        # Block requests from banned subscriptions
        if hasattr(request, 'config') and request.config.subscription.is_banned:
            return False

        # Validate referer header exists (same-origin policy)
        referer = request.META.get('HTTP_REFERER')
        if not referer:
            self._debug_log("No referer header")
            return False

        # Hostname validation with settings-based configuration
        if not self._validate_hostname(request, referer):
            return False

        # Validate custom CSRF token matches server-generated token
        token = self.get_token(request)
        client_token = request.META.get('HTTP_X_CSRF_TOKEN')
        if not client_token == token:
            self._debug_log(f"Token mismatch: client='{client_token}' server='{token}'")
            return False

        self._debug_log("CSRF validation passed")
        return True

    def _validate_hostname(self, request, referer):
        """
        Validate referer hostname for widget API requests.

        Widgets are designed to work cross-domain, so this validation focuses on:
        1. Same-origin requests (development/testing)
        2. Cross-origin widget requests (production client installations)

        Security is maintained through CSRF token validation, not hostname restrictions.

        Args:
            request: Django HttpRequest object
            referer: Referer URL string

        Returns:
            bool: True if hostname validation passes, False otherwise
        """
        from django.conf import settings

        # Get CSRF settings with fallback defaults
        csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
        ignore_port = csrf_settings.get('ignore_port_in_hostname_check', False)
        trusted_hostnames = csrf_settings.get('trusted_hostnames', [])

        # Parse hostnames
        referer_hostname = urlparse(referer).hostname
        request_host = request.META.get('HTTP_HOST', '')

        # Extract request hostname (with or without port based on settings)
        if ignore_port and ':' in request_host:
            request_hostname = request_host.split(':')[0]
        else:
            request_hostname = request_host

        self._debug_log(f"Hostname validation: referer='{referer_hostname}' request='{request_hostname}' ignore_port={ignore_port}")

        # Check direct hostname match (same-origin requests)
        if referer_hostname == request_hostname:
            self._debug_log("Same-origin request allowed")
            return True

        # Check against trusted hostnames list (if configured)
        if trusted_hostnames and referer_hostname in trusted_hostnames:
            self._debug_log(f"Hostname '{referer_hostname}' found in trusted list")
            return True

        # If ignore_port is enabled, also check if referer matches request hostname without port
        if ignore_port and ':' in request_host:
            base_request_hostname = request_host.split(':')[0]
            if referer_hostname == base_request_hostname:
                self._debug_log("Same-origin request allowed (port ignored)")
                return True

        # For widgets: Allow cross-origin requests if they have valid CSRF tokens
        # This is the key difference - widgets are SUPPOSED to work cross-domain
        # Security is maintained through the CSRF token validation in is_allowed()
        self._debug_log(f"Cross-origin widget request from '{referer_hostname}' - will validate CSRF token")
        return True

    def _debug_log(self, message):
        """
        Log debug messages for CSRF validation if debug mode is enabled.

        Args:
            message: Debug message to log
        """
        from django.conf import settings

        csrf_settings = getattr(settings, 'WIDGET_CSRF_SETTINGS', {})
        if csrf_settings.get('debug_csrf_validation', False):
            # Debug logging can be enabled via WIDGET_CSRF_SETTINGS.debug_csrf_validation
            pass

    def get_token(self, request):
        """
        Generate CSRF token based on User-Agent header.

        This uses the GitHub master algorithm that matches the repository version.
        This is the algorithm that should be used consistently across environments.

        Args:
            request: Django HttpRequest object

        Returns:
            str: Generated CSRF token
        """
        user_agent = request.META.get('HTTP_USER_AGENT')
        # GitHub master algorithm
        token = base64.b64encode(user_agent.encode('utf-8')).decode('utf-8')
        token_slice = token[:32]  # slice(0, 30 + 2)

        result = []
        for i in range(len(token_slice)):
            # JavaScript: token[27 + 11 - (7 + i * 11) % 39]
            index = (27 + 11 - (7 + i * 11) % 39) % len(token)
            result.append(token[index])

        return ''.join(result)


class WidgetProxyView(WsProtectMixin, ProxyView):
    """
    Default API proxy view for widget API requests.

    This view handles API proxying for calc widgets and other non-finder widgets.
    It uses the default API version (v2) and includes widget configuration
    filtering and rate limiting.

    Features:
    - CSRF protection via WsProtectMixin
    - Rate limiting via throttle classes
    - Widget configuration filtering
    - Chinese language parameter handling
    """
    _ignore_model_permissions = True
    return_raw = True

    throttle_classes = [
        WidgetProxyDayThrottle,
        WidgetProxyMinuteThrottle,
    ]

    def get_proxy_host(self):
        """
        Get upstream API host for default widgets.

        Ensures v2 API is used by appending /v2 to the base host.
        """
        from django.conf import settings
        host = getattr(settings, 'REST_PROXY', {}).get('HOST')
        return f'{host}/v2'

    def get_proxy_request_headers(self, request):
        """
        Add required authentication headers for the upstream API.
        """
        from django.conf import settings
        headers = super().get_proxy_request_headers(request)
        secret_token = getattr(settings, 'REST_PROXY', {}).get('X-WS-API-SECRET-TOKEN')
        if secret_token:
            headers['X-WS-API-SECRET-TOKEN'] = secret_token
        return headers

    def get_request_params(self, request):
        """
        Build request parameters for the proxied API call.

        Adds widget-specific filtering parameters and language settings
        based on the widget configuration.

        Args:
            request: Django HttpRequest object

        Returns:
            dict: Parameters to send to the upstream API

        Raises:
            Http404: If widget type doesn't allow API access
        """
        # Call ProxyView's get_request_params explicitly to avoid inheritance issues
        params = ProxyView.get_request_params(self, request)

        # Add widget-specific filtering if configuration exists
        if hasattr(request, 'config') and not request.config.is_default:
            # Check if widget type allows API access
            if not request.config.widget_type.allow_api:
                raise Http404

            # Add content filtering parameters from widget configuration
            filter_params = request.config.params.get_filter_params()
            params = dict(params)
            params.update(filter_params)
            params.update(self.get_chineze_params(request))

        return params

    def get_chineze_params(self, request):
        """
        Add Chinese language parameters if widget is configured for Chinese.

        Args:
            request: Django HttpRequest object

        Returns:
            dict: Language parameters for Chinese localization
        """
        params = {}
        if request.config.lang in WIDGET_CHINESE_LANG_CHOICES:
            params['lang'] = API_CHINESE_LANG

        return params


class FinderWidgetProxyView(WsProtectMixin, ProxyView):
    """
    Custom proxy view specifically for finder widget API requests.

    The finder widget requires v1 API endpoints while other widgets (like calc)
    use v2 API endpoints. This view automatically ensures the correct API version
    is used for finder widget requests.

    Key differences from WidgetProxyView:
    - Forces v1 API endpoint usage via get_proxy_host()
    - No additional parameter filtering (finder uses simpler API calls)
    - Same CSRF protection and rate limiting as other widgets

    URL Mapping Examples:
    - /widget/finder/api/mk → https://api3.wheel-size.com/v1/makes/
    - /widget/finder/api/tw → https://api3.wheel-size.com/v1/__tw/
    - /widget/finder/api/rd → https://api3.wheel-size.com/v1/__rd/
    """
    _ignore_model_permissions = True
    return_raw = True

    def get_proxy_host(self):
        """
        Override to ensure v1 API version for finder widget endpoints.

        The finder widget specifically requires v1 API endpoints, while
        other widgets may use v2. This method ensures the correct API
        version is used regardless of the default configuration.

        Returns:
            str: API host URL with v1 endpoint path
        """
        from django.conf import settings

        host = settings.REST_PROXY.get('HOST', '')

        # Ensure the host includes /v1 for finder widget API calls
        # This handles cases where REST_PROXY.HOST might be configured
        # for v2 API but finder needs v1
        if not host.endswith('/v1'):
            host = host.rstrip('/') + '/v1'

        return host


class FinderV2WidgetProxyView(WsProtectMixin, ProxyView):
    """
    Custom proxy view specifically for finder-v2 widget API requests.

    The finder-v2 widget uses v2 API endpoints with enhanced functionality
    including support for both primary and alternative flow types.

    Key features:
    - Uses v2 API endpoints via get_proxy_host()
    - Supports flow-aware API routing based on widget configuration
    - Same CSRF protection and rate limiting as other widgets

    URL Mapping Examples:
    - /widget/finder-v2/api/mk → https://api3.wheel-size.com/v2/makes/
    - /widget/finder-v2/api/ml → https://api3.wheel-size.com/v2/models/
    - /widget/finder-v2/api/sm → https://api3.wheel-size.com/v2/search/by_model/
    """
    _ignore_model_permissions = True
    return_raw = True

    throttle_classes = [
        WidgetProxyDayThrottle,
        WidgetProxyMinuteThrottle,
    ]

    def get_proxy_host(self):
        """
        Get upstream API host for finder-v2 widgets.

        Ensures v2 API is used by appending /v2 to the base host.
        """
        from django.conf import settings
        host = getattr(settings, 'REST_PROXY', {}).get('HOST')
        return f'{host}/v2'

    def get_proxy_request_headers(self, request):
        """
        Add required authentication headers for the upstream API.
        """
        from django.conf import settings
        headers = super().get_proxy_request_headers(request)
        secret_token = getattr(settings, 'REST_PROXY', {}).get('X-WS-API-SECRET-TOKEN')
        if secret_token:
            headers['X-WS-API-SECRET-TOKEN'] = secret_token
        return headers

    def get_request_params(self, request):
        """
        Build request parameters with proper list parameter handling.
        
        Override ProxyView.get_request_params to preserve multiple values
        for the same parameter name (e.g., region=val1&region=val2).
        
        For finder-v2 widget, we include widget configuration filtering
        to support region and brand filtering functionality.
        """
        params = {}

        # Handle query parameters while preserving multiple values.
        # DRF Request has .query_params, standard HttpRequest has .GET.
        raw_qp_source = getattr(request, "query_params", None)
        if raw_qp_source is None:
            raw_qp_source = getattr(request, "GET", None)

        if raw_qp_source:
            qp = raw_qp_source.copy()
            # Remove disallowed parameters (copy logic from parent ProxyView)
            for param in self.proxy_settings.DISALLOWED_PARAMS:
                if param in qp:
                    del qp[param]

            # ----
            # Normalise parameter names
            # -------------------------
            # Axios (and many browser libraries) encode list parameters as
            #   region[]=usdm&region[]=cdm
            # while the upstream Wheel-Size API expects *repeated* keys **without**
            # the square-bracket suffix:
            #   region=usdm&region=cdm
            # If we forward the `region[]` name verbatim the API silently
            # ignores it, which is why Chinese-only makes such as "Aion" were
            # still leaking into the response (see issue 2025-06-19).
            #
            # We therefore strip the trailing "[]" so the key becomes `region`.
            for raw_key in qp.keys():
                values = qp.getlist(raw_key)

                normalised_key = raw_key[:-2] if raw_key.endswith("[]") else raw_key

                if len(values) == 1:
                    params[normalised_key] = values[0]
                else:
                    params[normalised_key] = values

        # Add widget-specific filtering if configuration exists
        if hasattr(request, 'config') and not request.config.is_default:
            # Check if widget type allows API access
            if not request.config.widget_type.allow_api:
                raise Http404

            # Add content filtering parameters from widget configuration
            filter_params = request.config.params.get_filter_params()
            
            # Special handling for region parameters to merge arrays instead of replacing
            if 'region' in filter_params:
                widget_regions = filter_params['region']
                if isinstance(widget_regions, list):
                    # Get existing regions from URL parameters
                    url_regions = params.get('region', [])
                    if not isinstance(url_regions, list):
                        url_regions = [url_regions] if url_regions else []
                    
                    # Behaviour:
                    # 1. If the request URL already contains explicit region
                    #    filters, *trust* them and discard the regions stored in
                    #    the widget configuration. This allows the configuration
                    #    UI to override previous selections instantly.
                    # 2. Otherwise fall back to whatever is defined in the widget
                    #    config JSON.
                    if url_regions:
                        merged_regions = url_regions  # Use only URL regions
                    else:
                        merged_regions = list(widget_regions)  # Fallback to widget config
                    
                    params['region'] = merged_regions
                    # Remove region from filter_params to avoid double-update
                    filter_params = {k: v for k, v in filter_params.items() if k != 'region'}
            
            params.update(filter_params)
            params.update(self.get_chineze_params(request))

        return params

    def get_chineze_params(self, request):
        """
        Add Chinese language parameters if needed.

        For finder-v2, we keep this simple without widget configuration dependency.

        Args:
            request: Django HttpRequest object

        Returns:
            dict: Language parameters for Chinese localization
        """
        # For now, return empty dict - can be enhanced later if needed
        # The request parameter is kept for consistency with parent class signature
        return {}
    
    def create_response(self, response):
        """
        Override create_response to add custom sorting for search/by_model endpoint.
        
        For the /sm (search/by_model) endpoint, we need to sort the wheel data:
        1. First by is_stock (True before False)  
        2. Then by rim_diameter (ascending)
        
        This ensures stock wheels appear first, followed by aftermarket wheels,
        with each group sorted by rim diameter from smallest to largest.
        """
        import logging
        logger = logging.getLogger(__name__)
        
        # Get the source path to determine if this is a search/by_model request
        source_path = self.get_source_path()
        logger.info(f"FinderV2WidgetProxyView.create_response: source_path={source_path}")
        
        if source_path == 'search/by_model/':
            logger.info(f"Processing search/by_model response, status={response.status_code}, return_raw={self.return_raw}")
            # Only process if we're using raw response mode and have successful response
            if (self.return_raw or self.proxy_settings.RETURN_RAW) and response.status_code < 400:
                try:
                    import json
                    from django.http import HttpResponse
                    
                    # Parse the JSON response
                    data = json.loads(response.text)
                    logger.info(f"Parsed JSON data, type={type(data)}, keys={list(data.keys()) if isinstance(data, dict) else 'N/A'}")
                    
                    # Handle different response formats for wheel sorting
                    sorted_anything = False
                    
                    if isinstance(data, list):
                        # Direct array of wheel data (original sample format)
                        logger.info(f"Found direct wheel array, length={len(data)}")
                        if data and 'is_stock' in data[0]:
                            logger.info(f"Original first item: is_stock={data[0].get('is_stock')}, rim_diameter={data[0].get('front', {}).get('rim_diameter')}")
                            
                            data.sort(key=lambda x: (
                                not x.get('is_stock', False),
                                x.get('front', {}).get('rim_diameter') or 0
                            ))
                            
                            logger.info(f"Sorted first item: is_stock={data[0].get('is_stock')}, rim_diameter={data[0].get('front', {}).get('rim_diameter')}")
                            sorted_anything = True
                    
                    elif isinstance(data, dict) and 'data' in data:
                        # Response wrapped in {"data": [...]} format
                        modifications = data['data']
                        if isinstance(modifications, list):
                            logger.info(f"Found modifications array, length={len(modifications)}")
                            
                            # Sort wheels within each modification
                            for modification in modifications:
                                if isinstance(modification, dict) and 'wheels' in modification:
                                    wheels = modification['wheels']
                                    if isinstance(wheels, list) and wheels:
                                        logger.info(f"Sorting {len(wheels)} wheels in modification {modification.get('slug', 'unknown')}")
                                        
                                        if wheels and 'is_stock' in wheels[0]:
                                            # Log original order
                                            logger.info(f"Original first wheel: is_stock={wheels[0].get('is_stock')}, rim_diameter={wheels[0].get('front', {}).get('rim_diameter')}")
                                            
                                            # Sort wheels by is_stock first, then rim_diameter
                                            wheels.sort(key=lambda x: (
                                                not x.get('is_stock', False),
                                                x.get('front', {}).get('rim_diameter') or 0
                                            ))
                                            
                                            # Log sorted order
                                            logger.info(f"Sorted first wheel: is_stock={wheels[0].get('is_stock')}, rim_diameter={wheels[0].get('front', {}).get('rim_diameter')}")
                                            sorted_anything = True
                    
                    if sorted_anything:
                        logger.info("Successfully sorted wheel data")
                    else:
                        logger.info("No wheel data found to sort")
                    
                    # Return the sorted response
                    logger.info("Returning sorted response")
                    return HttpResponse(
                        json.dumps(data),
                        status=response.status_code,
                        content_type=response.headers.get('content-type')
                    )
                except (json.JSONDecodeError, ValueError) as e:
                    # If JSON parsing fails, fall back to original response
                    logger.error(f"JSON parsing failed: {e}")
                    pass
            else:
                logger.info(f"Not processing response: return_raw={self.return_raw}, proxy_return_raw={self.proxy_settings.RETURN_RAW}, status={response.status_code}")
        
        # For all other cases, use the default behavior
        logger.info("Using default create_response")
        return super().create_response(response)
