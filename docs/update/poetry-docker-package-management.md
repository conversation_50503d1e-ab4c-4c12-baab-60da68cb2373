# Poetry Docker Package Management Guide

Last Modified: 2025-01-21 18:00 UTC+6

## Overview

This guide explains how to properly manage Python packages using Poetry in Docker containers for the wheel-size-services project. It addresses common issues with virtual environments, package downgrades, and provides tools for efficient package management.

## The Problem with Poetry in Docker

### Default Poetry Behavior
Poetry creates virtual environments by default, which conflicts with <PERSON><PERSON>'s containerized approach where the entire container is the "virtual environment."

### Common Issues
1. **Virtual Environment Creation**: Poetry creates `.venv` directories even when configured not to
2. **Package Location Mismatch**: Packages installed in virtual environment but Django runs with system Python
3. **Import Failures**: `ModuleNotFoundError` when Django tries to import packages
4. **Package Downgrades**: Poetry downgrades packages to match lock file versions

## Understanding Package Downgrades

### Why Downgrades Happen

When you see output like this:
```
Package operations: 1 install, 15 updates, 0 removals
  - Downgrading jmespath (1.0.1 -> 0.10.0)
  - Downgrading python-dateutil (2.9.0.post0 -> 2.8.1)
  - Downgrading boto3 (1.38.24 -> 1.38.23)
  - Installing django-turnstile (0.1.2)
```

**Root Cause**: Poetry enforces exact versions from `poetry.lock` file to ensure reproducible builds.

### Why This Happens
1. **Lock File Age**: `poetry.lock` contains older versions that were tested and locked
2. **Environment Mismatch**: Current environment has newer versions from different installations
3. **Dependency Conflicts**: New packages require specific versions of existing packages
4. **Private PyPI Constraints**: WS packages may have specific version requirements

### Package Categories Affected

**AWS SDK Ecosystem:**
- `boto3` and `botocore` are tightly coupled
- Version mismatches cause authentication issues

**Django Ecosystem:**
- Django packages often have interdependencies
- Version conflicts can break admin interface or forms

**Python Utilities:**
- Core packages like `python-dateutil` affect many other packages
- Downgrades ensure compatibility across the entire dependency tree

## Correct Docker Poetry Commands

### The Problem with Standard Commands
```bash
# ❌ This creates a virtual environment:
poetry update

# ❌ This also creates a virtual environment:
docker exec ws_services poetry update
```

### Correct Command Structure
```bash
docker exec ws_services bash -c "
    cd /code && 
    export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && 
    /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false && 
    /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false && 
    /root/.pyenv/versions/3.12.0/bin/poetry [COMMAND]
"
```

### Common Operations

#### Update All Packages
```bash
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false && /root/.pyenv/versions/3.12.0/bin/poetry update"
```

#### Update Specific Packages
```bash
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false && /root/.pyenv/versions/3.12.0/bin/poetry update boto3 django-storages"
```

#### Add New Package
```bash
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false && /root/.pyenv/versions/3.12.0/bin/poetry add requests"
```

#### Remove Package
```bash
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false && /root/.pyenv/versions/3.12.0/bin/poetry remove django-recaptcha"
```

## Helper Tools

### 1. Docker Poetry Helper Script

Use the provided helper script at `scripts/docker-poetry.sh`:

```bash
# Update all packages
./scripts/docker-poetry.sh update

# Update specific packages
./scripts/docker-poetry.sh "update boto3 django-storages"

# Add new package
./scripts/docker-poetry.sh "add requests"

# Remove package
./scripts/docker-poetry.sh "remove django-recaptcha"

# Show installed packages
./scripts/docker-poetry.sh show

# Install dependencies (default)
./scripts/docker-poetry.sh
```

### 2. Docker Compose Override

Use the provided `docker-compose.poetry.yml` for complex operations:

```bash
# Update all packages
docker-compose -f docker-compose.yml -f docker-compose.poetry.yml run --rm poetry poetry update

# Update specific packages  
docker-compose -f docker-compose.yml -f docker-compose.poetry.yml run --rm poetry poetry update boto3 django-storages

# Add new package
docker-compose -f docker-compose.yml -f docker-compose.poetry.yml run --rm poetry poetry add requests
```

## Best Practices

### 1. Always Configure Poetry First
```bash
# Set these before every Poetry command in Docker:
poetry config virtualenvs.create false
poetry config virtualenvs.in-project false
```

### 2. Use Consistent Python Path
```bash
# Always export the correct Python path:
export PATH=/root/.pyenv/versions/3.12.0/bin:$PATH
```

### 3. Restart Container After Changes
```bash
# After any package changes:
docker-compose restart web
```

### 4. Verify Package Installation
```bash
# Check if package is importable:
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/python -c 'import package_name; print(\"Success\")'"
```

## Troubleshooting

### Check Virtual Environment Status
```bash
# Check if .venv directory exists (should not exist):
docker exec ws_services bash -c "ls -la /code/.venv"

# Check Poetry environment info:
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry env info"
```

### Check Package Installation Location
```bash
# Check system Python packages:
docker exec ws_services bash -c "ls -la /root/.pyenv/versions/3.12.0/lib/python3.12/site-packages/ | grep package_name"

# Check virtual environment packages (should be empty):
docker exec ws_services bash -c "ls -la /code/.venv/lib/python3.12/site-packages/ | grep package_name"
```

### Fix Virtual Environment Issues
```bash
# Remove problematic virtual environment:
docker exec ws_services bash -c "cd /code && rm -rf .venv"

# Reconfigure Poetry:
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false"

# Reinstall packages:
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry install --no-root"
```

## Package Update Strategies

### 1. Conservative Approach (Recommended)
Accept the downgrades from Poetry - they represent tested, compatible versions:
```bash
# Just install from lock file:
./scripts/docker-poetry.sh "install --no-root"
```

### 2. Selective Updates
Update only specific packages that need newer versions:
```bash
# Update specific packages:
./scripts/docker-poetry.sh "update boto3 botocore"
```

### 3. Full Update (Risky)
Update all packages to latest compatible versions:
```bash
# Update everything (test thoroughly):
./scripts/docker-poetry.sh update
```

## Prevention Measures

### 1. Dockerfile Improvements
```dockerfile
# Ensure proper Poetry configuration in Dockerfile:
RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false
RUN /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false
RUN /root/.pyenv/versions/3.12.0/bin/poetry install --no-root
# Add verification:
RUN /root/.pyenv/versions/3.12.0/bin/python -c "import django; print('Django imported successfully')"
```

### 2. Regular Maintenance
```bash
# Monthly lock file updates:
./scripts/docker-poetry.sh update
docker-compose build web  # Rebuild with new versions
```

### 3. Version Pinning for Critical Packages
```toml
# In pyproject.toml, pin critical packages:
django = "4.2.21"  # Exact version instead of "^4.2.21"
boto3 = "1.38.23"  # Pin to tested version
```

## Related Documentation

- [Cloudflare Turnstile Docker 502 Fix](./cloudflare-turnstile-docker-502-fix.md)
- [Docker Container Troubleshooting](./docker-container-troubleshooting.md)
- [Python Package Management Best Practices](./python-package-management-best-practices.md)

## Key Takeaways

1. **Always disable virtual environments** in Docker containers
2. **Use helper scripts** to avoid repetitive command construction
3. **Package downgrades are normal** and often beneficial for stability
4. **Test thoroughly** after any package updates
5. **Restart containers** after package changes
6. **Verify package imports** to catch issues early
