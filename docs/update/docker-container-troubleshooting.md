# Docker Container Troubleshooting Guide

Last Modified: 2025-01-21 18:00 UTC+6

## Overview

This guide provides comprehensive troubleshooting steps for common Docker container issues in the wheel-size-services project, with focus on Python package management, service startup problems, and environment configuration issues.

## Common Issues and Solutions

### 1. 502 Bad Gateway Errors

#### Symptoms
- Service returns 502 Bad Gateway
- Container appears to be running but service is inaccessible
- Django fails to start properly

#### Diagnostic Steps
```bash
# Check container status
docker ps

# Check container logs
docker logs ws_services --tail 20

# Check if Django process is running
docker exec ws_services bash -c "ps aux | grep manage.py"

# Test internal connectivity
docker exec ws_services bash -c "curl -I http://localhost:8000/"
```

#### Common Causes
1. **Python Import Errors**: Missing packages or wrong installation location
2. **Environment Variables**: Missing or incorrect configuration
3. **Database Connection**: Database not accessible or misconfigured
4. **Port Conflicts**: Port already in use by another service

### 2. Python Package Import Errors

#### Symptoms
- `ModuleNotFoundError: No module named 'package_name'`
- Django fails to start with import errors
- Packages show in `poetry show` but can't be imported

#### Diagnostic Steps
```bash
# Check if package is installed in system Python
docker exec ws_services bash -c "ls -la /root/.pyenv/versions/3.12.0/lib/python3.12/site-packages/ | grep package_name"

# Check if package is in virtual environment (should not exist)
docker exec ws_services bash -c "ls -la /code/.venv/lib/python3.12/site-packages/ | grep package_name"

# Test package import
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/python -c 'import package_name; print(\"Success\")'"

# Check Poetry environment
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry env info"
```

#### Solutions
```bash
# Remove virtual environment if it exists
docker exec ws_services bash -c "cd /code && rm -rf .venv"

# Configure Poetry correctly
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.create false && /root/.pyenv/versions/3.12.0/bin/poetry config virtualenvs.in-project false"

# Reinstall packages
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/poetry install --no-root"

# Restart container
docker-compose restart web
```

### 3. Container Won't Start

#### Symptoms
- Container exits immediately after starting
- `docker-compose up` shows container stopping
- Exit code 1 or other non-zero exit codes

#### Diagnostic Steps
```bash
# Check container exit status
docker ps -a

# Check container logs for startup errors
docker logs ws_services

# Try running container interactively
docker run -it --rm ws_services bash

# Check Dockerfile syntax
docker build -t test-image -f docker/Dockerfile .
```

#### Common Solutions
```bash
# Rebuild container from scratch
docker-compose down
docker-compose build --no-cache web
docker-compose up -d web

# Check for file permission issues
docker exec ws_services bash -c "ls -la /code/"

# Verify environment variables
docker exec ws_services bash -c "env | grep -E '(DJANGO|DATABASE|REDIS)'"
```

### 4. Database Connection Issues

#### Symptoms
- Django can't connect to database
- `django.db.utils.OperationalError`
- Database-related 500 errors

#### Diagnostic Steps
```bash
# Check if database container is running
docker ps | grep postgres

# Test database connectivity from web container
docker exec ws_services bash -c "pg_isready -h db -p 5432"

# Check database logs
docker logs ws_services_db

# Test Django database connection
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/python src/manage.py dbshell"
```

#### Solutions
```bash
# Restart database container
docker-compose restart db

# Check database environment variables
docker exec ws_services bash -c "env | grep DATABASE"

# Run Django migrations
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/python src/manage.py migrate"
```

### 5. Redis Connection Issues

#### Symptoms
- Cache-related errors
- Session storage issues
- Celery task failures

#### Diagnostic Steps
```bash
# Check if Redis container is running
docker ps | grep redis

# Test Redis connectivity
docker exec ws_services bash -c "redis-cli -h redis ping"

# Check Redis logs
docker logs ws_services_redis

# Test Django cache
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/python -c 'from django.core.cache import cache; cache.set(\"test\", \"value\"); print(cache.get(\"test\"))'"
```

## Environment Configuration Issues

### Missing Environment Variables

#### Check Required Variables
```bash
# Check all environment variables
docker exec ws_services bash -c "env | sort"

# Check specific Django settings
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/python -c 'from django.conf import settings; print(settings.DEBUG)'"

# Verify .env.local is loaded
docker exec ws_services bash -c "cat /code/.env.local"
```

#### Add Missing Variables
```bash
# Edit .env.local file
vim .env.local

# Restart container to load new variables
docker-compose restart web
```

### Python Path Issues

#### Check Python Configuration
```bash
# Check Python version
docker exec ws_services bash -c "python --version"

# Check Python path
docker exec ws_services bash -c "python -c 'import sys; print(sys.path)'"

# Check which Python Django is using
docker exec ws_services bash -c "which python"

# Check pyenv configuration
docker exec ws_services bash -c "pyenv versions"
```

## Performance Issues

### High Memory Usage

#### Diagnostic Steps
```bash
# Check container memory usage
docker stats ws_services

# Check processes inside container
docker exec ws_services bash -c "ps aux --sort=-%mem | head -10"

# Check Django memory usage
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && /root/.pyenv/versions/3.12.0/bin/python -c 'import psutil; print(f\"Memory: {psutil.virtual_memory().percent}%\")'"
```

### Slow Startup Times

#### Diagnostic Steps
```bash
# Time container startup
time docker-compose up -d web

# Check what's taking time during startup
docker logs ws_services -f

# Profile Django startup
docker exec ws_services bash -c "cd /code && export PATH=/root/.pyenv/versions/3.12.0/bin:\$PATH && time /root/.pyenv/versions/3.12.0/bin/python src/manage.py check"
```

## Useful Diagnostic Commands

### Container Information
```bash
# Container details
docker inspect ws_services

# Container resource usage
docker stats ws_services --no-stream

# Container processes
docker exec ws_services bash -c "ps aux"

# Container disk usage
docker exec ws_services bash -c "df -h"
```

### Network Diagnostics
```bash
# Check container networking
docker network ls
docker network inspect wheel-size-services_default

# Test connectivity between containers
docker exec ws_services bash -c "ping db"
docker exec ws_services bash -c "ping redis"

# Check port bindings
docker port ws_services
```

### File System Diagnostics
```bash
# Check file permissions
docker exec ws_services bash -c "ls -la /code/"

# Check disk space
docker exec ws_services bash -c "du -sh /code/*"

# Check for large files
docker exec ws_services bash -c "find /code -type f -size +100M"
```

## Recovery Procedures

### Complete Container Reset
```bash
# Stop all services
docker-compose down

# Remove containers and volumes
docker-compose down -v

# Remove images (optional)
docker rmi $(docker images -q wheel-size-services*)

# Rebuild everything
docker-compose build --no-cache
docker-compose up -d
```

### Partial Reset (Preserve Data)
```bash
# Stop services
docker-compose down

# Rebuild only web service
docker-compose build web

# Start services
docker-compose up -d
```

### Emergency Access
```bash
# Access container even if service is failing
docker run -it --rm --volumes-from ws_services ws_services bash

# Or create new container with same volumes
docker run -it --rm -v wheel-size-services_code:/code ws_services bash
```

## Prevention and Monitoring

### Health Checks
```bash
# Add to docker-compose.yml:
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
```

### Logging Configuration
```bash
# Configure logging in docker-compose.yml:
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### Regular Maintenance
```bash
# Weekly cleanup
docker system prune -f

# Monthly image updates
docker-compose pull
docker-compose build --pull
docker-compose up -d
```

## Related Documentation

- [Cloudflare Turnstile Docker 502 Fix](./cloudflare-turnstile-docker-502-fix.md)
- [Poetry Docker Package Management](./poetry-docker-package-management.md)
- [Python Package Management Best Practices](./python-package-management-best-practices.md)

## Emergency Contacts and Resources

### Quick Reference Commands
```bash
# Container status
docker ps -a

# Service logs
docker logs ws_services --tail 50 -f

# Interactive shell
docker exec -it ws_services bash

# Restart service
docker-compose restart web

# Full rebuild
docker-compose up --build -d web
```

### Common File Locations
- **Django Settings**: `/code/src/settings/`
- **Environment Variables**: `/code/.env.local`
- **Python Packages**: `/root/.pyenv/versions/3.12.0/lib/python3.12/site-packages/`
- **Application Code**: `/code/src/`
- **Static Files**: `/code/src/static/`
